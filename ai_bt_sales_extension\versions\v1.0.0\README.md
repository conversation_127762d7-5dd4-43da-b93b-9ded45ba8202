# Sales Agent Commission Implementation - v1.0.0

## Overview
This version implements the Sales Agent Commission tracking functionality, mirroring the Thekedar Costs implementation from the purchase module.

## Directory Structure
```
ai_bt_sales_extension/
├── models/
│   ├── __init__.py
│   ├── sales_agent_commission.py
│   ├── sale_order_extension.py
│   └── res_partner_extension.py
├── views/
│   ├── sales_agent_commission_views.xml
│   └── sale_order_views.xml
├── security/
│   └── ir.model.access.csv
└── __manifest__.py
```

## Installation
1. Install all required dependencies
2. Install the module through Odoo apps interface
3. Configure user access rights
4. Create sales agents by marking partners with the sales agent flag

## Configuration
1. Mark partners as sales agents using the new flag in partner form
2. Configure commission fees values in sale orders
3. Assign sales agents to sale orders

## Usage
1. Create a sale order
2. Select a sales agent
3. Enable commission fees if applicable
4. Confirm the order to create commission record
5. Track and manage commissions through the new menu item

For detailed changes, refer to CHANGELOG.md

## For Security CSV File
access_sales_agent_commission_manager,sales.agent.commission.manager,model_sales_agent_commission,sales_team.group_sale_manager,1,1,1,1
access_sales_agent_commission_user,sales.agent.commission.user,model_sales_agent_commission,sales_team.group_sale_salesman,1,1,1,0
