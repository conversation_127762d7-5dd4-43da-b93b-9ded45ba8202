<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Product Selection Wizard Form View -->
    <record id="view_product_selection_wizard_form" model="ir.ui.view">
        <field name="name">product.selection.wizard.form</field>
        <field name="model">product.selection.wizard</field>
        <field name="arch" type="xml">
            <form string="Select Product">
                <header>
                    <button name="action_select_product" string="Select Product" type="object" class="btn-primary"/>
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                </header>
                <sheet>
                    <div class="oe_title">
                        <h1>
                            <field name="lot_number" readonly="1"/>
                        </h1>
                        <h3>Multiple products found for this lot number. Please select one:</h3>
                    </div>

                    <field name="source_model" invisible="1"/>
                    <field name="source_record_id" invisible="1"/>

                    <group string="Available Products">
                        <field name="line_ids" nolabel="1">
                            <list create="false" delete="false" editable="bottom">
                                <field name="is_selected" widget="boolean_toggle"/>
                                <field name="product_code"/>
                                <field name="product_name"/>
                                <field name="product_uom"/>
                                <field name="total_qty_available" string="Total Available"/>
                                <field name="product_id" invisible="1"/>
                            </list>
                        </field>
                    </group>

                    <group string="Stock Information by Location">
                        <field name="line_ids" nolabel="1" readonly="1">
                            <list create="false" delete="false" edit="false">
                                <field name="product_name" string="Product"/>
                                <field name="total_qty_available" string="Total Available"/>
                                <field name="stock_details_text" string="Stock Details" widget="text"/>
                            </list>
                        </field>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Product Selection Wizard Action -->
    <record id="action_product_selection_wizard" model="ir.actions.act_window">
        <field name="name">Select Product</field>
        <field name="res_model">product.selection.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
        <field name="view_id" ref="view_product_selection_wizard_form"/>
    </record>
</odoo>
