<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <!-- Inherit the mrp.production form view and add the custom fields -->
    <record id="mrp_production_subcontract_form_inherit" model="ir.ui.view">
        <field name="name">mrp.production.subcontract.form</field>
        <field name="model">mrp.production</field>
        <field name="inherit_id" ref="mrp.mrp_production_form_view"/>
        <field name="arch" type="xml">
            <xpath expr="//header" position="inside">
                <button name="button_calculate"
                        string="Calculate"
                        type="object"
                        class="oe_highlight"
                        invisible="state in ['done', 'cancel']"
                        help="Calculate costs and rates without marking as done"/>
                <button name="reset_cost_analysis_fields"
                        string="Reset Cost Analysis"
                        type="object"
                        class="oe_highlight"
                        invisible="state != 'done'"
                        groups="mrp.group_mrp_manager"
                        help="Reset all cost analysis fields to zero"/>
                <button name="button_recalculate_weight_analysis"
                        string="Recalculate Weight Analysis"
                        type="object"
                        class="oe_highlight"
                        invisible="state != 'done'"
                        groups="mrp.group_mrp_manager"
                        help="Recalculate all weight-related fields"/>
                <button name="update_other_material_amount_directly"
                        string="Update Other Material Amount"
                        type="object"
                        class="oe_highlight"
                        invisible="state != 'done'"
                        groups="mrp.group_mrp_manager"
                        help="Directly update other material amount based on the sum of total_cost for other materials"/>
                <!-- <button name="button_force_calculate_amounts"
                        string="Force Calculate Amounts"
                        type="object"
                        class="oe_highlight"
                        groups="mrp.group_mrp_manager"
                        help="Force calculation of product amounts for problematic MOs"/> -->
            </xpath>
            <xpath expr="//group[@name='group_extra_info']" position="inside">
                <field name="report_number" />
                <field name="is_production_b"/>
                <field name="x_is_subcontracting"/>
                <field name="x_subcontractor_id" invisible="x_is_subcontracting == False" required="x_is_subcontracting == True"/>

            </xpath>

            <xpath expr="//field[@name='move_raw_ids']" position="replace">
                <separator string="Raw Material"/>

                        <field name="move_raw_material_ids" widget="stock_move_one2many"
                            context="{'default_date': date_start,
                                      'default_date_deadline': date_start,
                                      'default_location_id': location_src_id,
                                      'default_location_dest_id': production_location_id,
                                      'default_warehouse_id': warehouse_id,
                                      'default_state': 'draft',
                                      'default_picking_type_id': picking_type_id,
                                      'default_company_id': company_id,
                                      'form_view_ref': 'mrp.view_mrp_stock_move_operations',
                                      'active_mo_id': id}"
                            readonly="state in ['cancel', 'done']"
                            string="Raw Material">
                            <list default_order="is_done, manual_consumption desc, sequence" editable="bottom">
                                <control>
                                    <create string="Add a line"/>
                                    <button name="action_add_from_catalog_raw" string="Catalog" type="object" class="px-4 btn-link" context="{'order_id': parent.id}"/>
                                </control>
                                <field name="product_id" force_save="1" context="{'default_is_storable': True}" required="1" readonly="move_lines_count &gt; 0 or state == 'cancel' or (state != 'draft' and not additional)"/>

                                <field name="product_uom" column_invisible="True"/>
                                <field name="propagate_cancel" column_invisible="True"/>
                                <field name="price_unit" column_invisible="True"/>
                                <field name="company_id" column_invisible="True"/>
                                <field name="product_uom_category_id" column_invisible="True"/>
                                <field name="name" column_invisible="True"/>
                                <field name="allowed_operation_ids" column_invisible="True"/>
                                <field name="unit_factor" column_invisible="True"/>
                                <field name="date_deadline" column_invisible="True" force_save="1"/>
                                <field name="date" column_invisible="True"/>
                                <field name="additional" column_invisible="True"/>
                                <field name="picking_type_id" column_invisible="True"/>
                                <field name="is_storable" column_invisible="True"/>
                                <field name="has_tracking" column_invisible="True"/>
                                <field name="operation_id" column_invisible="True"/>
                                <field name="is_done" column_invisible="True"/>
                                <field name="bom_line_id" column_invisible="True"/>
                                <field name="sequence" column_invisible="True"/>
                                <field name="warehouse_id" column_invisible="True"/>
                                <field name="is_locked" column_invisible="True"/>
                                <field name="move_lines_count" column_invisible="True"/>
                                <field name="location_dest_id" domain="[('id', 'child_of', parent.location_dest_id)]" column_invisible="True"/>
                                <field name="state" column_invisible="True" force_save="1"/>
                                <field name="should_consume_qty" column_invisible="True"/>
                                <field name="product_uom_qty" force_save="1" string="To Consume" column_invisible="not parent.show_produce_all" readonly="parent.state != 'draft' and ((parent.state not in ('confirmed', 'progress', 'to_close') and not parent.is_planned) or (parent.is_locked and state != 'draft'))"/>
                                <field name="product_uom_qty" widget="mrp_should_consume" force_save="1" string="To Consume" column_invisible="parent.show_produce_all" readonly="parent.state != 'draft' and ((parent.state not in ('confirmed', 'progress', 'to_close') and not parent.is_planned) or (parent.is_locked and state != 'draft'))"/>
                                <field name="product_qty" readonly="1" column_invisible="True"/>
                                <field name="forecast_expected_date" column_invisible="True"/>
                                <button type="object" name="action_product_forecast_report" title="Forecast Report" icon="fa-area-chart" column_invisible="parent.state != 'draft'" invisible="forecast_availability &lt; 0"/>
                                <button type="object" name="action_product_forecast_report" title="Forecast Report" icon="fa-area-chart text-danger" column_invisible="parent.state != 'draft'" invisible="forecast_availability &gt;= 0"/>
                                <field name="forecast_availability" column_invisible="parent.state in ['done', 'cancel']" string="Forecast" widget="forecast_widget" optional="hide"/>
                                <field name="quantity" string="Quantity" column_invisible="parent.state == 'draft'" decoration-info="manual_consumption" decoration-bf="manual_consumption" readonly="has_tracking != 'none'" force_save="1"  sum="Total Quantity"/>
                                <field name="product_uom" readonly="state != 'draft' and id" options="{'no_open': True, 'no_create': True}" groups="uom.group_uom"/>
                                <field name="x_product_loose_weight" sum="Total Loose Weight"/>
                                <field name="x_product_total_weight" readonly="1" sum="Total Weight"/>
                                <!-- <field name="location_id" string="From" readonly="1" force_save="1" groups="stock.group_stock_multi_locations" optional="show"/> -->
                                <field name="picked" string="Consumed" column_invisible="parent.state == 'draft'" optional="show"/>
                                <field name="manual_consumption" column_invisible="True" force_save="1"/>
                                <field name="show_details_visible" column_invisible="True"/>
                                <field name="lot_ids" widget="many2many_tags" readonly="1" optional="show" string="Lot/Serial Numbers" help="Select lot/serial numbers for this material" groups="stock.group_production_lot" domain="[('product_id','=',product_id)]" context="{'default_product_id': product_id, 'default_company_id': company_id}"/>
                                <!-- readonly="0" -->
                                <field name="group_id" column_invisible="True"/>
                                <field name="actual_cost" readonly="0" />
                                <field name="total_cost" readonly="0" sum="Total Amount"/>
                            </list>
                        </field>
                <separator string="Other Material"/>

                        <field name="move_other_ids" widget="stock_move_one2many"
                            context="{'default_date': date_start,
                                    'default_date_deadline': date_start,
                                    'default_location_id': location_src_id,
                                    'default_location_dest_id': production_location_id,
                                    'default_warehouse_id': warehouse_id,
                                    'default_state': 'draft',
                                    'default_picking_type_id': picking_type_id,
                                    'default_company_id': company_id,
                                    'form_view_ref': 'mrp.view_mrp_stock_move_operations',
                                    'active_mo_id': id}"
                            readonly="state in ['cancel', 'done']"
                            string="Other Material">
                            <list default_order="is_done, manual_consumption desc, sequence" editable="bottom">
                                <control>
                                    <create string="Add a line"/>
                                    <button name="action_add_from_catalog_raw" string="Catalog" type="object" class="px-4 btn-link" context="{'order_id': parent.id}"/>
                                </control>
                                <field name="product_id" context="{'default_is_storable': True}" domain="[('id', '!=', parent.product_id)]" required="1" readonly="state == 'done'" width="250"/>
                                <field name="product_uom" column_invisible="True"/>
                                <field name="propagate_cancel" column_invisible="True"/>
                                <field name="price_unit" column_invisible="True"/>
                                <field name="company_id" column_invisible="True"/>
                                <field name="product_uom_category_id" column_invisible="True"/>
                                <field name="name" column_invisible="True"/>
                                <field name="allowed_operation_ids" column_invisible="True"/>
                                <field name="unit_factor" column_invisible="True"/>
                                <field name="date_deadline" column_invisible="True" force_save="1"/>
                                <field name="date" column_invisible="True"/>
                                <field name="additional" column_invisible="True"/>
                                <field name="picking_type_id" column_invisible="True"/>
                                <field name="is_storable" column_invisible="True"/>
                                <field name="has_tracking" column_invisible="True"/>
                                <field name="operation_id" column_invisible="True"/>
                                <field name="is_done" column_invisible="True"/>
                                <field name="bom_line_id" column_invisible="True"/>
                                <field name="sequence" column_invisible="True"/>
                                <field name="warehouse_id" column_invisible="True"/>
                                <field name="is_locked" column_invisible="True"/>
                                <field name="move_lines_count" column_invisible="True"/>
                                <field name="location_dest_id" domain="[('id', 'child_of', parent.location_dest_id)]" column_invisible="True"/>
                                <field name="state" column_invisible="True" force_save="1"/>
                                <field name="should_consume_qty" column_invisible="True"/>
                                <field name="product_uom_qty" force_save="1" string="To Consume" column_invisible="not parent.show_produce_all" readonly="parent.state != 'draft' and ((parent.state not in ('confirmed', 'progress', 'to_close') and not parent.is_planned) or (parent.is_locked and state != 'draft'))"/>
                                <field name="product_uom_qty" widget="mrp_should_consume" force_save="1" string="To Consume" column_invisible="parent.show_produce_all" readonly="parent.state != 'draft' and ((parent.state not in ('confirmed', 'progress', 'to_close') and not parent.is_planned) or (parent.is_locked and state != 'draft'))"/>
                                <field name="product_qty" readonly="1" column_invisible="True"/>
                                <field name="forecast_expected_date" column_invisible="True"/>
                                <button type="object" name="action_product_forecast_report" title="Forecast Report" icon="fa-area-chart" column_invisible="parent.state != 'draft'" invisible="forecast_availability &lt; 0"/>
                                <button type="object" name="action_product_forecast_report" title="Forecast Report" icon="fa-area-chart text-danger" column_invisible="parent.state != 'draft'" invisible="forecast_availability &gt;= 0"/>
                                <field name="forecast_availability" column_invisible="parent.state in ['done', 'cancel']" string="Forecast" widget="forecast_widget" optional="hide"/>
                                <field name="quantity" string="Quantity" column_invisible="parent.state == 'draft'" decoration-info="manual_consumption" decoration-bf="manual_consumption" readonly="has_tracking != 'none'" force_save="1" sum="Total Quantity"/>
                                <field name="product_uom" readonly="state != 'draft' and id" options="{'no_open': True, 'no_create': True}" groups="uom.group_uom"/>
                                <field name="x_product_loose_weight" string="Loose Weight" sum="Total Loose Weight"/>
                                <field name="x_product_total_weight" string="Total Weight" readonly="1" sum="Total Weight"/>
                                <field name="location_id" string="Source Location" readonly="1" force_save="1" groups="stock.group_stock_multi_locations"/>
                                <field name="picked" string="Consumed" column_invisible="parent.state == 'draft'" optional="show"/>
                                <field name="manual_consumption" column_invisible="True" force_save="1"/>
                                <field name="show_details_visible" column_invisible="True"/>
                                <field name="lot_ids" widget="many2many_tags" readonly="1" optional="show" string="Lot/Serial Numbers" help="Select lot/serial numbers for this material" groups="stock.group_production_lot" domain="[('product_id','=',product_id)]" context="{'default_product_id': product_id, 'default_company_id': company_id}"/>

                                <field name="group_id" invisible="1"/>
                                <field name="actual_cost" readonly="0" />
                                <field name="total_cost" readonly="0" sum="Total Amount"/>
                            </list>
                        </field>
                <separator string="Bag Information"/>
                    <field name="bag_line_ids" >
                        <list editable="bottom">
                            <field name="bag_type"/>
                            <field name="quantity" sum="Total"/>
                            <field name="cost" readonly="1" />
                        </list>
                    </field>
                    <group>
                        <field name="total_bag_cost" readonly="1"/>
                    </group>

                <!-- <group class="oe_subtotal_footer">
                    <field name="move_raw_material_ids_total_weight" readonly="1"/>
                    <field name="move_other_ids_total_weight" readonly="1"/>
                    <field name="move_byproduct_sale_ids_total_weight" readonly="1"/>
                    <field name="move_byproduct_sale_ids_total_amount" readonly="1"/>
                    <field name="raw_material_cost" readonly="1"/>
                    <field name="hamali_cost" readonly="1"/>
                    <field name="sortex_landed_cost" readonly="1" />
                    <field name="ci_landed_cost" readonly="1" />
                    <field name="total_cost" readonly="1" />
                </group> -->
            </xpath>

            <xpath expr="//field[@name='move_byproduct_ids']" position="replace">

                    <separator string="Finish Products"/>
                    <!-- <h3>Sale Products By-Products</h3> -->
                    <field name="move_byproduct_sale_ids" widget="stock_move_one2many"
                            context="{'default_date': date_finished,
                                      'default_date_deadline': date_deadline,
                                      'default_location_id': production_location_id,
                                      'default_location_dest_id': location_dest_id,
                                      'default_state': 'draft',
                                      'default_production_id': id,
                                      'default_picking_type_id': picking_type_id,
                                      'default_company_id': company_id,
                                      'form_view_ref': 'mrp.view_mrp_stock_move_operations',
                                      'default_sale_product': True}"
                            readonly="state in ['cancel', 'done']"
                            options="{'delete': [('state', '=', 'draft')]}"
                            domain="[('sale_product', '=', True)]">
                        <list default_order="is_done,sequence" decoration-muted="is_done" editable="bottom">
                            <control>
                                <create string="Add a line"/>
                                <button name="action_add_from_catalog_byproduct" string="Catalog" type="object" class="px-4 btn-link" context="{'order_id': parent.id}"/>
                            </control>
                            <field name="product_id" context="{'default_is_storable': True}" domain="[('id', '!=', parent.product_id)]" required="1" readonly="state == 'done'" width="250"/>
                            <field name="sale_product" readonly="state == 'done'"/>
                            <field name="location_dest_id" string="To" readonly="1" force_save="1" groups="stock.group_stock_multi_locations"/>
                            <field name="company_id" column_invisible="True"/>
                            <field name="product_uom_category_id" column_invisible="True"/>
                            <field name="name" column_invisible="True"/>
                            <field name="allowed_operation_ids" column_invisible="True"/>
                            <field name="unit_factor" column_invisible="True"/>
                            <field name="date" column_invisible="True"/>
                            <field name="date_deadline" column_invisible="True" force_save="1"/>
                            <field name="additional" column_invisible="True"/>
                            <field name="picking_type_id" column_invisible="True"/>
                            <field name="has_tracking" column_invisible="True"/>
                            <field name="operation_id" column_invisible="True"/>
                            <field name="is_done" column_invisible="True"/>
                            <field name="bom_line_id" column_invisible="True"/>
                            <field name="byproduct_id" column_invisible="True"/>
                            <field name="sequence" column_invisible="True"/>
                            <field name="location_id" column_invisible="True"/>
                            <field name="warehouse_id" column_invisible="True"/>
                            <field name="is_locked" column_invisible="True"/>
                            <field name="move_lines_count" column_invisible="True"/>
                            <field name="state" column_invisible="True" force_save="1"/>
                            <field name="product_uom_qty" string="To Produce" force_save="1" readonly="state in ['done', 'cancel']"/>
                            <field name="quantity" column_invisible="parent.state == 'draft'" readonly="state in ['done', 'cancel']"/>
                            <field name="x_product_loose_weight" sum="Total Loose Weight"/>
                            <field name="x_product_total_weight" readonly="1" sum="Total Weight" />
                            <field name="picked" string="Produced" column_invisible="parent.state == 'draft'" optional="show"/>
                            <field name="product_uom" groups="uom.group_uom"/>
                            <field name="cost_share" sum="Total Cost"/>
                            <field name="price_unit"/>
                            <field name="by_product_rate" readonly="state in ['done', 'cancel']"/>
                            <field name="by_product_rate_total" readonly="state in ['done', 'cancel']" sum="Total Cost"/>
                            <field name="show_details_visible" column_invisible="True"/>
                            <field name="lot_ids" widget="many2many_tags" string="Lot/Serial Numbers" help="Displays the produced Lot/Serial Numbers." groups="stock.group_production_lot" readonly="1" column_invisible="not parent.show_lot_ids or parent.state == 'draft'" invisible="not show_details_visible" options="{'create': [('parent.use_create_components_lots', '!=', False)]}" context="{'default_product_id': product_id}" domain="[('product_id','=',product_id)]"/>
                            <field name="party_moves" widget="many2many_tags" readonly="1" />
                            <button name="action_open_party_moves" type="object" string="View Party Details" class="oe-link" icon="fa-eye" />




                        </list>
                    </field>
                    <separator string="Riclin Products"/>
                    <!-- <h3>Riclin Products By-Products</h3> -->
                    <field name="move_byproduct_riclin_ids" widget="stock_move_one2many"
                            context="{'default_date': date_finished,
                                      'default_date_deadline': date_deadline,
                                      'default_location_id': production_location_id,
                                      'default_location_dest_id': location_dest_id,
                                      'default_state': 'draft',
                                      'default_production_id': id,
                                      'default_picking_type_id': picking_type_id,
                                      'default_company_id': company_id,
                                      'form_view_ref': 'mrp.view_mrp_stock_move_operations',
                                      'default_riclin_product': True}"
                            readonly="state in ['cancel', 'done']"
                            options="{'delete': [('state', '=', 'draft')]}"
                            domain="[('riclin_product', '=', True)]">
                        <list default_order="is_done,sequence" decoration-muted="is_done" editable="bottom">
                            <control>
                                <create string="Add a line"/>
                                <button name="action_add_from_catalog_byproduct" string="Catalog" type="object" class="px-4 btn-link" context="{'order_id': parent.id}"/>
                            </control>
                            <field name="product_id" context="{'default_is_storable': True}" domain="[('id', '!=', parent.product_id)]" required="1" readonly="state == 'done'" width="250"/>
                            <field name="riclin_product" readonly="state == 'done'"/>
                            <field name="location_dest_id" string="To" readonly="1" force_save="1" groups="stock.group_stock_multi_locations"/>
                            <field name="company_id" column_invisible="True"/>
                            <field name="product_uom_category_id" column_invisible="True"/>
                            <field name="name" column_invisible="True"/>
                            <field name="allowed_operation_ids" column_invisible="True"/>
                            <field name="unit_factor" column_invisible="True"/>
                            <field name="date" column_invisible="True"/>
                            <field name="date_deadline" column_invisible="True" force_save="1"/>
                            <field name="additional" column_invisible="True"/>
                            <field name="picking_type_id" column_invisible="True"/>
                            <field name="has_tracking" column_invisible="True"/>
                            <field name="operation_id" column_invisible="True"/>
                            <field name="is_done" column_invisible="True"/>
                            <field name="bom_line_id" column_invisible="True"/>
                            <field name="byproduct_id" column_invisible="True"/>
                            <field name="sequence" column_invisible="True"/>
                            <field name="location_id" column_invisible="True"/>
                            <field name="warehouse_id" column_invisible="True"/>
                            <field name="is_locked" column_invisible="True"/>
                            <field name="move_lines_count" column_invisible="True"/>
                            <field name="state" column_invisible="True" force_save="1"/>
                            <field name="product_uom_qty" string="To Produce" force_save="1" readonly="state in ['done', 'cancel']"/>
                            <field name="quantity" column_invisible="parent.state == 'draft'" readonly="state in ['done', 'cancel']"/>
                            <field name="x_product_loose_weight" sum="Total Loose Weight"/>
                            <field name="x_product_total_weight" readonly="1" sum="Total Weight"/>
                            <field name="picked" string="Produced" column_invisible="parent.state == 'draft'" optional="show"/>
                            <field name="product_uom" groups="uom.group_uom"/>
                            <field name="cost_share" sum="Total Cost"/>
                            <field name="price_unit"/>
                            <field name="by_product_rate" readonly="state in ['done', 'cancel']"/>
                            <field name="by_product_rate_total" readonly="state in ['done', 'cancel']" sum="Total Cost"/>
                            <field name="show_details_visible" column_invisible="True"/>
                            <field name="lot_ids" widget="many2many_tags" string="Lot/Serial Numbers" help="Displays the produced Lot/Serial Numbers." groups="stock.group_production_lot" readonly="1" column_invisible="not parent.show_lot_ids or parent.state == 'draft'" invisible="not show_details_visible" options="{'create': [('parent.use_create_components_lots', '!=', False)]}" context="{'default_product_id': product_id}" domain="[('product_id','=',product_id)]"/>
                             </list>
                    </field>

                    <separator string="Crushing Products"/>
                    <!-- <h3>Clean Products By-Products</h3> -->
                    <field name="move_byproduct_clean_ids" widget="stock_move_one2many"
                            context="{'default_date': date_finished,
                                      'default_date_deadline': date_deadline,
                                      'default_location_id': production_location_id,
                                      'default_location_dest_id': location_dest_id,
                                      'default_state': 'draft',
                                      'default_production_id': id,
                                      'default_picking_type_id': picking_type_id,
                                      'default_company_id': company_id,
                                      'form_view_ref': 'mrp.view_mrp_stock_move_operations',
                                      'default_clean_product': True}"
                            readonly="state in ['cancel', 'done']"
                            options="{'delete': [('state', '=', 'draft')]}"
                            domain="[('clean_product', '=', True)]">
                        <list default_order="is_done,sequence" decoration-muted="is_done" editable="bottom">
                            <control>
                                <create string="Add a line"/>
                                <button name="action_add_from_catalog_byproduct" string="Catalog" type="object" class="px-4 btn-link" context="{'order_id': parent.id}"/>
                            </control>
                            <field name="product_id" context="{'default_is_storable': True}" domain="[('id', '!=', parent.product_id)]" required="1" readonly="state == 'done'" width="250"/>
                            <field name="clean_product" readonly="state == 'done'"/>
                            <field name="location_dest_id" string="To" readonly="1" force_save="1" groups="stock.group_stock_multi_locations"/>
                            <field name="company_id" column_invisible="True"/>
                            <field name="product_uom_category_id" column_invisible="True"/>
                            <field name="name" column_invisible="True"/>
                            <field name="allowed_operation_ids" column_invisible="True"/>
                            <field name="unit_factor" column_invisible="True"/>
                            <field name="date" column_invisible="True"/>
                            <field name="date_deadline" column_invisible="True" force_save="1"/>
                            <field name="additional" column_invisible="True"/>
                            <field name="picking_type_id" column_invisible="True"/>
                            <field name="has_tracking" column_invisible="True"/>
                            <field name="operation_id" column_invisible="True"/>
                            <field name="is_done" column_invisible="True"/>
                            <field name="bom_line_id" column_invisible="True"/>
                            <field name="byproduct_id" column_invisible="True"/>
                            <field name="sequence" column_invisible="True"/>
                            <field name="location_id" column_invisible="True"/>
                            <field name="warehouse_id" column_invisible="True"/>
                            <field name="is_locked" column_invisible="True"/>
                            <field name="move_lines_count" column_invisible="True"/>
                            <field name="state" column_invisible="True" force_save="1"/>
                            <field name="product_uom_qty" string="To Produce" force_save="1" readonly="state in ['done', 'cancel']"/>
                            <field name="quantity" column_invisible="parent.state == 'draft'" readonly="state in ['done', 'cancel']"/>
                            <field name="x_product_loose_weight" sum="Total Loose Weight"/>
                            <field name="x_product_total_weight" readonly="1" sum="Total Weight"/>
                            <field name="picked" string="Produced" column_invisible="parent.state == 'draft'" optional="show"/>
                            <field name="product_uom" groups="uom.group_uom"/>
                            <field name="cost_share" readonly="state in ['done', 'cancel']" sum="Total Cost"/>
                            <field name="price_unit"/>
                            <field name="by_product_rate" readonly="state in ['done', 'cancel']"/>
                            <field name="by_product_rate_total" readonly="state in ['done', 'cancel']" sum="Total Cost"/>
                            <field name="show_details_visible" column_invisible="True"/>
                            <field name="lot_ids" widget="many2many_tags" string="Lot/Serial Numbers" help="Displays the produced Lot/Serial Numbers." groups="stock.group_production_lot" readonly="1" column_invisible="not parent.show_lot_ids or parent.state == 'draft'" invisible="not show_details_visible" options="{'create': [('parent.use_create_components_lots', '!=', False)]}" context="{'default_product_id': product_id}" domain="[('product_id','=',product_id)]"/>

                        </list>
                    </field>

                    <separator string="Waste Products"/>
                    <!-- <h3>Clean Products By-Products</h3> -->
                    <field name="move_byproduct_waste_ids" widget="stock_move_one2many"
                            context="{'default_date': date_finished,
                                      'default_date_deadline': date_deadline,
                                      'default_location_id': production_location_id,
                                      'default_location_dest_id': location_dest_id,
                                      'default_state': 'draft',
                                      'default_production_id': id,
                                      'default_picking_type_id': picking_type_id,
                                      'default_company_id': company_id,
                                      'form_view_ref': 'mrp.view_mrp_stock_move_operations',
                                      'default_waste_product': True}"
                            readonly="state in ['cancel', 'done']"
                            options="{'delete': [('state', '=', 'draft')]}"
                            domain="[('waste_product', '=', True)]">
                        <list default_order="is_done,sequence" decoration-muted="is_done" editable="bottom">
                            <control>
                                <create string="Add a line"/>
                                <button name="action_add_from_catalog_byproduct" string="Catalog" type="object" class="px-4 btn-link" context="{'order_id': parent.id}"/>
                            </control>
                            <field name="product_id" context="{'default_is_storable': True}" domain="[('id', '!=', parent.product_id)]" required="1" readonly="state == 'done'" width="250"/>
                            <field name="waste_product" readonly="state == 'done'"/>
                            <field name="location_dest_id" string="To" readonly="1" force_save="1" groups="stock.group_stock_multi_locations"/>
                            <field name="company_id" column_invisible="True"/>
                            <field name="product_uom_category_id" column_invisible="True"/>
                            <field name="name" column_invisible="True"/>
                            <field name="allowed_operation_ids" column_invisible="True"/>
                            <field name="unit_factor" column_invisible="True"/>
                            <field name="date" column_invisible="True"/>
                            <field name="date_deadline" column_invisible="True" force_save="1"/>
                            <field name="additional" column_invisible="True"/>
                            <field name="picking_type_id" column_invisible="True"/>
                            <field name="has_tracking" column_invisible="True"/>
                            <field name="operation_id" column_invisible="True"/>
                            <field name="is_done" column_invisible="True"/>
                            <field name="bom_line_id" column_invisible="True"/>
                            <field name="byproduct_id" column_invisible="True"/>
                            <field name="sequence" column_invisible="True"/>
                            <field name="location_id" column_invisible="True"/>
                            <field name="warehouse_id" column_invisible="True"/>
                            <field name="is_locked" column_invisible="True"/>
                            <field name="move_lines_count" column_invisible="True"/>
                            <field name="state" column_invisible="True" force_save="1"/>
                            <field name="product_uom_qty" string="To Produce" force_save="1" readonly="state in ['done', 'cancel']"/>
                            <field name="quantity" column_invisible="parent.state == 'draft'" readonly="state in ['done', 'cancel']"/>
                            <field name="x_product_loose_weight" sum="Total Loose Weight"/>
                            <field name="x_product_total_weight" readonly="1" sum="Total Weight"/>
                            <field name="picked" string="Produced" column_invisible="parent.state == 'draft'" optional="show"/>
                            <field name="product_uom" groups="uom.group_uom"/>
                            <field name="cost_share" readonly="state in ['done', 'cancel']" sum="Total Cost"/>
                            <field name="price_unit"/>
                            <field name="by_product_rate" readonly="state in ['done', 'cancel']"/>
                            <field name="by_product_rate_total" readonly="state in ['done', 'cancel']" sum="Total Cost"/>
                            <field name="show_details_visible" column_invisible="True"/>
                            <field name="lot_ids" widget="many2many_tags" string="Lot/Serial Numbers" help="Displays the produced Lot/Serial Numbers." groups="stock.group_production_lot" readonly="1" column_invisible="not parent.show_lot_ids or parent.state == 'draft'" invisible="not show_details_visible" options="{'create': [('parent.use_create_components_lots', '!=', False)]}" context="{'default_product_id': product_id}" domain="[('product_id','=',product_id)]"/>
                         </list>
                    </field>

            </xpath>
            <xpath expr="//notebook " position="after">
                <notebook>
                <page string="Cost Analysis" name="cost_analysis">
                    <group>
                        <group string="Raw Material Costs">
                            <field name="kachi_raw_material_amount"/>
                            <field name="other_material_amount"/>
                            <field name="total_material_cost"/>
                        </group>
                        <group string="Additional Costs">
                            <field name="hamali_cost" readonly='1'/>
                            <field name="sortex_landed_cost" readonly='1'/>
                            <field name="total_bag_cost" readonly='1'/>
                            <field name="total_additional_cost" sum="Total"/>
                        </group>
                        <group string="Final Calculations">
                            <field name="total_cost_with_kachi"/>
                            <field name="ci_landed_cost"/>
                            <field name="raw_material_grand_amount"/>
                            <field name="per_kg_cost"/>
                             <field name="actual_cost" readonly="1" />
                            <field name="final_raw_material_cost"/>
                            <field name="total_final_cost"/>
                        </group>
                        <group string="Product Amounts">
                            <field name="finished_goods_amount"/>
                            <field name="reclean_goods_amount"/>
                            <field name="crushing_goods_amount"/>
                            <field name="wastage_goods_amount"/>
                            <field name="total_amount"/>

                        </group>
                        <group string="Profit/Loss">
                            <field name="total_sales_amount"/>
                            <field name="profit_loss"/>
                            <field name="recommended_price"/>
                        </group>
                    </group>
                </page>
                <page string="Weight Analysis" name="weight_analysis">
                    <header>
                        <button name="button_recalculate_weight_analysis"
                                string="Recalculate Weight Analysis"
                                type="object"
                                class="oe_highlight"
                                groups="mrp.group_mrp_manager"
                                help="Recalculate all weight-related fields"/>
                    </header>
                    <group>
                        <group string="Raw Materials Weight">
                            <field name="total_raw_material_weight"/>
                            <field name="total_other_material_weight"/>
                            <field name="total_weight"/>
                            <field name="raw_material_weight_percentage"/>
                            <field name="other_material_weight_percentage"/>
                        </group>
                        <group string="Finished Products Weight">
                            <field name="finished_goods_weight"/>
                            <field name="reclean_goods_weight"/>
                            <field name="crushing_goods_weight"/>
                            <field name="wastage_goods_weight"/>
                            <field name="total_finished_weight"/>
                            <field name="missing_weight"/>
                        </group>
                        <group string="Finished Products Percentage">
                            <field name="finished_goods_percentage"/>
                            <field name="reclean_goods_percentage"/>
                            <field name="crushing_goods_percentage"/>
                            <field name="wastage_goods_percentage"/>
                            <field name="missing_weight_percentage"/>
                        </group>
                        <group string="Cost Share Percentages">
                            <field name="cost_share_finished"/>
                            <field name="cost_share_reclean"/>
                            <field name="cost_share_crushing"/>
                            <field name="cost_share_wastage"/>
                            <field name="total_cost_share"/>
                        </group>
                    </group>
                </page>
                <page string="Hamali &amp; Sortex Rate Configuration">
                    <group string="Cost Rate Configuration">
                        <field name="hamali_rate" readonly="state in ('done', 'cancel')"/>
                        <field name="sortex_rate" readonly="state in ('done', 'cancel')"/>
                    </group>
                </page>

                </notebook>
            </xpath>
        </field>
    </record>

    <record id="view_party_id_form" model="ir.ui.view">
        <field name="name">party.id.form</field>
        <field name="model">party.id</field>
        <field name="arch" type="xml">
            <form string="Party ID">
                <sheet>
                    <group>
                    <field name="name"/>
                        <field name="bag_type"/>
                        <field name="bag_price"/>
                        <field name="bag_qty" sum="Total Quantity"/>
                        <field name="bag_cost" sum="Total Cost"/>
                        <field name="weight_sold" sum="Total Weight"/>
                        <field name="sell_cost"/>
                        <field name="total_amount" sum="Total"/>
                    </group>
                </sheet>
            </form>
        </field>
    </record>


<record id="view_party_id_list" model="ir.ui.view">
    <field name="name">party.id.list</field>
    <field name="model">party.id</field>
    <field name="arch" type="xml">
        <list editable="bottom">  <!-- Add editable="bottom" here -->
            <field name="name"/>
            <field name="bag_type"/>
            <field name="bag_price"/>
            <field name="bag_qty" sum="Total Quantity"/>
            <field name="bag_cost" sum="Total Cost"/>
            <field name="weight_sold" sum="Total Weight"/>
            <field name="sell_cost" />
            <field name="total_amount" sum="Total"/>
        </list>
    </field>
</record>

    <record id="action_party_id" model="ir.actions.act_window">
        <field name="name">Party IDs</field>
        <field name="res_model">party.id</field>
        <field name="view_mode">list,form</field>
    </record>
<menuitem id="menu_party_id" name="Party IDs" parent="stock.menu_stock_root" action="action_party_id"/>

</odoo>
