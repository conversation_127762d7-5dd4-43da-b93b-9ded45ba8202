# Odoo Extra Addons

This directory contains custom modules for Odoo. It has been configured in the Odoo configuration file (`odoo.conf`) as an addons path.

## Structure

Each module should be placed in its own directory within this folder. For example:

```
extra-addons/
├── my_custom_module/
│   ├── __init__.py
│   ├── __manifest__.py
│   ├── models/
│   ├── views/
│   └── ...
├── another_module/
│   └── ...
└── README.md
```

## Adding a New Module

To create a new module:

1. Create a new directory with your module name
2. Add the required files (at minimum `__init__.py` and `__manifest__.py`)
3. Restart Odoo to detect the new module
4. Install the module from the Odoo Apps menu

## Best Practices

- Use a consistent naming convention for your modules
- Document your modules with proper README files
- Follow Odoo's coding standards and guidelines
- Keep modules focused on specific functionality
