from odoo import http
from odoo.http import request
from datetime import date, datetime
import qrcode
import base64
from io import BytesIO


class EmployeeProfile(http.Controller):
    @staticmethod
    def calculate_age(birthdate):
        if isinstance(birthdate, str):
            birthdate = datetime.strptime(birthdate, '%Y-%m-%d').date()
        today = date.today()
        age = today.year - birthdate.year - ((today.month, today.day) < (birthdate.month, birthdate.day))
        return age

    @staticmethod
    def generate_qr_code(url):
        qr = qrcode.QRCode(version=1, box_size=10, border=5)
        qr.add_data(url)
        qr.make(fit=True)
        img = qr.make_image(fill_color="black", back_color="white")

        buffered = BytesIO()
        img.save(buffered, format="PNG")
        return base64.b64encode(buffered.getvalue()).decode()

    @http.route(['/employee/profile/<int:employee_id>'], type='http', auth="public", website=True)
    def employee_profile(self, employee_id, **kw):
        employee = request.env['hr.employee'].sudo().browse(employee_id)
        if not employee.exists():
            return request.render('website.404')

        base_url = request.httprequest.url_root.rstrip('/')
        employee_url = f"{base_url}/employee/profile/{employee_id}"
        qr_code_image = self.generate_qr_code(employee_url)
        return request.render("ai_bt_spices_module.employee_profile_card", {
            'employee': employee,
            # 'image_data_uri': request.env['ir.qweb.field.image'].sudo().image_data_uri,
            'calculate_age': self.calculate_age,
            'qr_code_image': qr_code_image,
        })
