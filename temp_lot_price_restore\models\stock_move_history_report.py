# -*- coding: utf-8 -*-

from odoo import api, fields, models, tools, _
from odoo.exceptions import UserError
from odoo.tools.float_utils import float_is_zero


class StockMoveHistoryReport(models.Model):
    _name = 'stock.move.history.report'
    _description = 'Stock Move History Report'
    _order = 'date desc'

    id = fields.Integer('ID', readonly=True)
    reference = fields.Char('Reference', readonly=True)
    date = fields.Datetime('Scheduled Date', readonly=True, help='Scheduled date from the related picking')
    scheduled_date = fields.Datetime('Scheduled Date (Original)', readonly=True, help='Original scheduled date from the related picking')
    picking_id = fields.Many2one('stock.picking', 'Transfer', readonly=True)
    picking_type_id = fields.Many2one('stock.picking.type', 'Operation Type', readonly=True)
    picking_type_code = fields.Selection([
        ('incoming', 'Receipt'),
        ('outgoing', 'Delivery'),
        ('internal', 'Internal Transfer'),
        ('mrp_operation', 'Manufacturing')
    ], string='Type', readonly=True)
    product_id = fields.Many2one('product.product', 'Product', readonly=True)
    product_categ_id = fields.Many2one('product.category', 'Product Category', readonly=True)
    lot_id = fields.Many2one('stock.lot', 'Lot/Serial No', readonly=True)
    location_id = fields.Many2one('stock.location', 'From Location', readonly=True)
    location_dest_id = fields.Many2one('stock.location', 'To Location', readonly=True)
    warehouse_id = fields.Many2one('stock.warehouse', 'Warehouse', readonly=True)
    company_id = fields.Many2one('res.company', 'Company', readonly=True)
    state = fields.Selection([
        ('draft', 'Draft'),
        ('waiting', 'Waiting'),
        ('confirmed', 'Confirmed'),
        ('assigned', 'Available'),
        ('done', 'Done'),
        ('cancel', 'Cancelled')
    ], string='Status', readonly=True)
    quantity = fields.Float('Quantity', readonly=True)
    product_weight = fields.Float('Product Weight (kg)', readonly=True)
    total_weight = fields.Float('Total Weight (kg)', readonly=True)
    product_rate = fields.Float('Product Rate', readonly=True)
    total_value = fields.Float('Total Value', readonly=True)
    lot_price = fields.Float('Lot Price', readonly=True)
    price_source = fields.Selection([
        ('move_line', 'Move Line'),
        ('lot', 'Lot'),
        ('product', 'Product'),
        ('none', 'Not Available')
    ], string='Price Source', readonly=True)
    current_product_cost = fields.Float('Current Product Cost', readonly=True, help='Current cost of the product')
    forecasted_qty = fields.Float('Forecasted Quantity', readonly=True, help='Forecasted quantity in the destination location')
    warehouse_qty = fields.Float('Warehouse Quantity', readonly=True, help='Total quantity in the warehouse stock location')

    @api.model
    def populate_report(self):
        """Populate the report with data from stock moves"""
        # Clear existing data
        self.sudo().search([]).unlink()

        # Get warehouse locations
        warehouse_locations = {}
        for warehouse in self.env['stock.warehouse'].search([]):
            warehouse_locations[warehouse.id] = {
                'view_location_id': warehouse.view_location_id.id,
                'lot_stock_id': warehouse.lot_stock_id.id,
                'wh_input_stock_loc_id': warehouse.wh_input_stock_loc_id.id if hasattr(warehouse, 'wh_input_stock_loc_id') else False,
                'wh_output_stock_loc_id': warehouse.wh_output_stock_loc_id.id if hasattr(warehouse, 'wh_output_stock_loc_id') else False,
                'wh_pack_stock_loc_id': warehouse.wh_pack_stock_loc_id.id if hasattr(warehouse, 'wh_pack_stock_loc_id') else False,
                'wh_qc_stock_loc_id': warehouse.wh_qc_stock_loc_id.id if hasattr(warehouse, 'wh_qc_stock_loc_id') else False,
            }

        # Get location to warehouse mapping
        location_warehouse = {}
        for warehouse_id, locations in warehouse_locations.items():
            for location_type, location_id in locations.items():
                if location_id:
                    location_warehouse[location_id] = warehouse_id

        # Get stock moves
        moves = self.env['stock.move'].search([('state', '=', 'done')])

        # Create report records
        for move in moves:
            for move_line in move.move_line_ids:
                # Determine warehouse
                warehouse_id = False
                if move.picking_type_id and move.picking_type_id.code == 'outgoing':
                    warehouse_id = location_warehouse.get(move.location_id.id)
                else:
                    # For incoming, internal, mrp_operation, and any other types, use destination location's warehouse
                    warehouse_id = location_warehouse.get(move.location_dest_id.id)

                # Calculate product rate and total value
                product_rate = 0.0
                price_source = 'none'

                if move_line.standard_price and float(move_line.standard_price) > 0:
                    product_rate = float(move_line.standard_price)
                    price_source = 'move_line'
                elif move_line.lot_id and move_line.lot_id.avg_cost_per_weight and float(move_line.lot_id.avg_cost_per_weight) > 0 and move_line.product_id.weight > 0:
                    product_rate = float(move_line.lot_id.avg_cost_per_weight) * float(move_line.product_id.weight)
                    price_source = 'lot'
                elif move_line.product_id.standard_price and float(move_line.product_id.standard_price) > 0:
                    product_rate = float(move_line.product_id.standard_price)
                    price_source = 'product'

                # Calculate total value
                quantity = float(move_line.quantity) if move_line.quantity else 0.0
                total_value = product_rate * quantity

                # Calculate total weight
                product_weight = float(move_line.product_id.weight) if move_line.product_id.weight else 0.0
                total_weight = product_weight * quantity

                # Get lot price
                lot_price = 0.0
                if move_line.lot_id and move_line.lot_id.avg_cost_per_weight:
                    lot_price = float(move_line.lot_id.avg_cost_per_weight)

                # Create report record
                self.sudo().create({
                    'reference': move.reference,
                    'date': move.picking_id.scheduled_date if move.picking_id else move.date,
                    'scheduled_date': move.picking_id.scheduled_date if move.picking_id else False,
                    'picking_id': move.picking_id.id if move.picking_id else False,
                    'picking_type_id': move.picking_type_id.id if move.picking_type_id else False,
                    'picking_type_code': move.picking_type_id.code if move.picking_type_id else False,
                    'product_id': move.product_id.id,
                    'product_categ_id': move.product_id.categ_id.id,
                    'lot_id': move_line.lot_id.id if move_line.lot_id else False,
                    'location_id': move.location_id.id,
                    'location_dest_id': move.location_dest_id.id,
                    'warehouse_id': warehouse_id,
                    'company_id': move.company_id.id,
                    'state': move.state,
                    'quantity': quantity,
                    'product_weight': product_weight,
                    'total_weight': total_weight,
                    'product_rate': product_rate,
                    'total_value': total_value,
                    'lot_price': lot_price,
                    'price_source': price_source,
                    'current_product_cost': float(move.product_id.standard_price) if move.product_id.standard_price else 0.0,
                    'forecasted_qty': 0.0,
                })

        # Update forecasted quantities
        self.update_forecasted_qty()

        return {
            'type': 'ir.actions.client',
            'tag': 'reload',
        }

    def action_open_picking(self):
        self.ensure_one()
        return {
            'name': _('Transfer'),
            'type': 'ir.actions.act_window',
            'res_model': 'stock.picking',
            'view_mode': 'form',
            'res_id': self.picking_id.id,
            'context': self.env.context,
        }

    def action_open_product(self):
        self.ensure_one()
        return {
            'name': _('Product'),
            'type': 'ir.actions.act_window',
            'res_model': 'product.product',
            'view_mode': 'form',
            'res_id': self.product_id.id,
            'context': self.env.context,
        }

    def action_open_lot(self):
        self.ensure_one()
        if not self.lot_id:
            raise UserError(_("This move doesn't have a lot/serial number."))
        return {
            'name': _('Lot/Serial Number'),
            'type': 'ir.actions.act_window',
            'res_model': 'stock.lot',
            'view_mode': 'form',
            'res_id': self.lot_id.id,
            'context': self.env.context,
        }

    @api.model
    def update_forecasted_qty(self):
        """Update forecasted quantity for all records"""
        records = self.search([])
        for record in records:
            if record.product_id:
                # Get the forecasted quantity for this product in the move's location
                location_id = record.location_dest_id.id

                # If the location is available, get the forecasted quantity
                if location_id:
                    quants = self.env['stock.quant'].search([
                        ('product_id', '=', record.product_id.id),
                        ('location_id', '=', location_id)
                    ])
                    location_qty = sum(quants.mapped('quantity'))
                else:
                    location_qty = 0.0

                # Also get the total warehouse quantity if warehouse is available
                warehouse_qty = 0.0
                if record.warehouse_id:
                    warehouse_quants = self.env['stock.quant'].search([
                        ('product_id', '=', record.product_id.id),
                        ('location_id', '=', record.warehouse_id.lot_stock_id.id)
                    ])
                    warehouse_qty = sum(warehouse_quants.mapped('quantity'))

                # Update both forecasted quantity and warehouse quantity
                record.write({
                    'forecasted_qty': location_qty,
                    'warehouse_qty': warehouse_qty
                })

    @api.model
    def read_group(self, domain, fields, groupby, offset=0, limit=None, orderby=False, lazy=True):
        """Override read_group to update forecasted quantities before grouping"""
        # Update forecasted quantities if needed
        if 'forecasted_qty' in fields:
            self.update_forecasted_qty()
        return super(StockMoveHistoryReport, self).read_group(domain, fields, groupby, offset, limit, orderby, lazy)

    @api.model
    def search_read(self, domain=None, fields=None, offset=0, limit=None, order=None):
        """Override search_read to update forecasted quantities before reading"""
        # Update forecasted quantities if needed
        if fields and 'forecasted_qty' in fields:
            self.update_forecasted_qty()
        return super(StockMoveHistoryReport, self).search_read(domain, fields, offset, limit, order)
