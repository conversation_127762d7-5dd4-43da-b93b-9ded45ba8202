from odoo import api, fields, models, tools, _
from odoo.exceptions import UserError
from odoo.tools.float_utils import float_is_zero
from collections import defaultdict
import logging

_logger = logging.getLogger(__name__)

class StockValuationAdjustmentLines(models.Model):
    _inherit = 'stock.valuation.adjustment.lines'

    additional_landed_cost = fields.Monetary(
        'Additional Landed Cost', digits=(16, 9),
        help="Additional landed cost to be added to the cost of the products")

class StockLandedCost(models.Model):
    _inherit = 'stock.landed.cost'


    def _check_sum(self):
        """ Check if each cost line its valuation lines sum to the correct amount
        and if the overall total amount is correct also """
        prec_digits = self.env.company.currency_id.decimal_places
        for landed_cost in self:
            total_amount = sum(landed_cost.valuation_adjustment_lines.mapped('additional_landed_cost'))
            if not tools.float_is_zero(total_amount - landed_cost.amount_total, precision_digits=0):
                return False

            val_to_cost_lines = defaultdict(lambda: 0.0)
            for val_line in landed_cost.valuation_adjustment_lines:
                val_to_cost_lines[val_line.cost_line_id] += val_line.additional_landed_cost
            if any(not tools.float_is_zero(cost_line.price_unit - val_amount, precision_digits=0)
                   for cost_line, val_amount in val_to_cost_lines.items()):
                return False
        return True
        # return super()._check_sum()

    def compute_landed_cost(self):
        """Ensure proper cost distribution when using by_po_line_cost method"""
        AdjustmentLines = self.env['stock.valuation.adjustment.lines']
        
        for cost in self:
            # Clear existing adjustment lines
            cost.valuation_adjustment_lines.unlink()
            
            if any(line.split_method == 'by_po_line_cost' for line in cost.cost_lines):
                # Get all moves associated with this landed cost
                moves = cost.picking_ids.move_ids.filtered(
                    lambda m: m.stock_valuation_layer_ids & cost.stock_valuation_layer_ids
                )
                
                # Process each cost line separately
                for cost_line in cost.cost_lines:
                    total_cost = cost_line.price_unit
                    
                    # Group moves by lot_id to ensure separate calculations
                    moves_by_lot = {}
                    for move in moves:
                        for move_line in move.move_line_ids:
                            if move_line.lot_id:
                                lot_key = (move.product_id.id, move_line.lot_id.id)
                                if lot_key not in moves_by_lot:
                                    moves_by_lot[lot_key] = self.env['stock.move']
                                moves_by_lot[lot_key] |= move
                    
                    # Calculate total value for distribution based on stock valuation layers
                    total_value = sum(layer.value for move in moves for layer in move.stock_valuation_layer_ids)
                    
                    if total_value:
                        # Process each lot separately
                        for (product_id, lot_id), lot_moves in moves_by_lot.items():
                            lot_value = sum(layer.value for move in lot_moves for layer in move.stock_valuation_layer_ids)
                            
                            # Calculate proportional cost for this lot
                            lot_cost = (lot_value / total_value) * total_cost
                            
                            for move in lot_moves:
                                if move.stock_valuation_layer_ids:  # Only process moves with valuation layers
                                    move_value = sum(layer.value for layer in move.stock_valuation_layer_ids)
                                    
                                    # Calculate proportional cost for this move within the lot
                                    move_cost = (move_value / lot_value) * lot_cost if lot_value else 0
                                    
                                    # Use the quantity from the move's move_line_ids for this lot
                                    quantity = sum(line.quantity for line in move.move_line_ids.filtered(
                                        lambda l: l.lot_id.id == lot_id
                                    ))
                                    
                                    AdjustmentLines.create({
                                        'cost_id': cost.id,
                                        'cost_line_id': cost_line.id,  # Link to specific cost line
                                        'move_id': move.id,
                                        'product_id': move.product_id.id,
                                        'quantity': quantity,
                                        'former_cost': move_value,
                                        'additional_landed_cost': move_cost,
                                        'final_cost': move_value + move_cost
                                    })
                                    
                                    _logger.info(
                                        f"Created adjustment line for move {move.id}, cost line {cost_line.name}: "
                                        f"Product={move.product_id.name}, "
                                        f"Lot={self.env['stock.lot'].browse(lot_id).name}, "
                                        f"Qty={quantity}, "
                                        f"Original Cost={move_value}, "
                                        f"Additional={move_cost}, "
                                        f"Final={move_value + move_cost}"
                                    )
                
                # Verify the total adjustments match the total costs
                total_costs = sum(line.price_unit for line in cost.cost_lines)
                total_adjustments = sum(line.additional_landed_cost for line in cost.valuation_adjustment_lines)
                
                for line in cost.cost_lines:
                    logging.info(f"Cost Line - Price Unit: {line.price_unit:.6f}")

                # Log each valuation adjustment line
                for line in cost.valuation_adjustment_lines:
                    logging.info(f"Adjustment Line - Additional Landed Cost: {line.additional_landed_cost:.6f}")

                _logger.info(
                    f"Total Cost Calculations - Total costs: {total_costs}, "
                    f"Total adjustments: {total_adjustments}"
                    f"Is Float Zero: {float_is_zero(total_costs - total_adjustments, precision_digits=0)}"
                )
                if not float_is_zero(total_costs - total_adjustments, precision_digits=0):
                    _logger.warning(
                        f"Cost mismatch - Total costs: {total_costs}, "
                        f"Total adjustments: {total_adjustments}"
                    )
                    return False
                    
                return True
            
            return super().compute_landed_cost()

    def create_from_picking(self, picking):
        """Create landed costs from a stock picking."""
        if not picking.purchase_id:
            return False

        order = picking.purchase_id
        created_costs = self.env['stock.landed.cost']
        
        # Group moves by product
        moves_by_product = {}
        for move in picking.move_ids.filtered(lambda m: m.state == 'done' and m.product_id.type == 'consu'):
            if not move.product_id.x_is_bag:
                if move.product_id not in moves_by_product:
                    moves_by_product[move.product_id] = self.env['stock.move']
                moves_by_product[move.product_id] |= move

        # Create landed cost per product
        for product, moves in moves_by_product.items():
            purchase_line = moves[0].purchase_line_id
            if not purchase_line or not purchase_line.price_subtotal:
                continue

            # Get all lots for this product's moves
            lots = moves.mapped('lot_ids')
            if not lots:
                continue

            for lot in lots:
                try:
                    # Create landed cost record
                    landed_cost = self.create({
                        'picking_ids': [(6, 0, [picking.id])],
                        'date': fields.Date.today(),
                        'account_journal_id': self.env['account.journal'].search(
                            [('type', '=', 'general')], limit=1).id,
                        'stock_valuation_layer_ids': [(6, 0, moves.stock_valuation_layer_ids.ids)],
                        'target_model': 'picking',
                    })

                    # Add landed cost lines
                    self._add_landed_cost_lines(landed_cost, purchase_line, lot, order)
                    
                    # Compute and validate
                    landed_cost.compute_landed_cost()
                    _logger.info(f"Processed Landed Cost")
                    if landed_cost.valuation_adjustment_lines:
                        _logger.info(f"Validating Landed Cost")
                        landed_cost.button_validate()
                        created_costs |= landed_cost
                    else:
                        _logger.warning(f'No adjustment lines generated for lot {lot.name}')
                        landed_cost.unlink()
                        
                except Exception as e:
                    _logger.error(f'Error processing landed cost for lot {lot.name}: {str(e)}')
                    if landed_cost.exists():
                        landed_cost.unlink()
                    raise UserError(_('Failed to process landed cost: %s') % str(e))

        if created_costs:
            picking.write({'landed_costs_created': True})
        return created_costs

    def _add_landed_cost_lines(self, landed_cost, purchase_line, lot, order):
        """Helper method to add cost lines to landed cost record."""
        LandedCostLine = self.env['stock.landed.cost.lines']
        total_additional_cost = 0
        
        # Get received quantity from picking's move lines for this lot
        move_lines = self.env['stock.move.line'].search([
            ('lot_id', '=', lot.id),
            ('state', '=', 'done'),
            ('picking_id', 'in', landed_cost.picking_ids.ids)
        ])
        
        received_product_qty = sum(move_line.quantity for move_line in move_lines)
        received_ratio = round(received_product_qty / purchase_line.product_qty, 9) if purchase_line.product_qty else 0
        
        # Calculate bags per unit ratio with high precision
        # bags_per_unit = round(purchase_line.x_bag_quantity / purchase_line.product_qty, 9) if purchase_line.product_qty else 0
        # Calculate adjusted bag quantity based on received product quantity
        adjusted_bag_quantity = round(received_product_qty * received_ratio, 9) if received_ratio else 0
        
        _logger.info(
            f"Calculating costs for lot {lot.name}:\n"
            f"Original PO quantity: {purchase_line.product_qty}\n"
            f"Original bag quantity: {purchase_line.x_bag_quantity}\n"
            f"Received quantity: {received_product_qty}\n"
            # f"Bags per unit (9 decimals): {bags_per_unit}\n"
            f"Adjusted bag quantity (9 decimals): {adjusted_bag_quantity}"
        )

        # Commission Fee (based on value)
        if order.x_commission_fees:
            commission_rate = self._validate_fee_value(
                round(order.x_commission_fees_values / 100, 9),
                'Commission rate'
            )
            commission_amount = round((purchase_line.price_subtotal * received_ratio) * commission_rate, 9)
            if commission_amount > 0:
                total_additional_cost += commission_amount
                LandedCostLine.create({
                    'cost_id': landed_cost.id,
                    'product_id': self._get_or_create_landed_cost_product('Commission Fee').id,
                    'price_unit': commission_amount,
                    'split_method': 'by_po_line_cost',
                    'name': f'Commission {order.x_commission_fees_values}% - Lot {lot.name}'
                })
                _logger.info(f"Commission amount (9 decimals): {commission_amount}")
        
        # Brokerage Fee (based on value)
        if order.x_brokerage_fees:
            brokerage_rate = self._validate_fee_value(
                round(order.x_brokerage_fees_values / 100, 9),
                'Brokerage rate'
            )
            brokerage_amount = round((purchase_line.price_subtotal * received_ratio) * brokerage_rate, 9)
            if brokerage_amount > 0:
                total_additional_cost += brokerage_amount
                LandedCostLine.create({
                    'cost_id': landed_cost.id,
                    'product_id': self._get_or_create_landed_cost_product('Brokerage Fee').id,
                    'price_unit': brokerage_amount,
                    'split_method': 'by_po_line_cost',
                    'name': f'Brokerage {order.x_brokerage_fees_values}% - Lot {lot.name}'
                })
                _logger.info(f"Brokerage amount (9 decimals): {brokerage_amount}")

        # Market Fee (based on value)
        if order.x_market_fees:
            market_rate = self._validate_fee_value(
                round(order.x_market_fees_values / 100, 9),
                'Market rate'
            )
            market_amount = round(((purchase_line.x_bank_payment * purchase_line.x_product_total_weight  )  * received_ratio) * market_rate, 9)
            if market_amount > 0:
                total_additional_cost += market_amount
                LandedCostLine.create({
                    'cost_id': landed_cost.id,
                    'product_id': self._get_or_create_landed_cost_product('Market Fee').id,
                    'price_unit': market_amount,
                    'split_method': 'by_po_line_cost',
                    'name': f'Market Fee {order.x_market_fees_values}% - Lot {lot.name}'
                })
                _logger.info(f"Market fee amount (9 decimals): {market_amount}")

        # Transportation Cost (based on calculated bag quantity)
        if order.x_transportation_fees and adjusted_bag_quantity:
            transport_amount = round(adjusted_bag_quantity * order.x_transportation_fees_value, 9)
            if transport_amount > 0:
                total_additional_cost += transport_amount
                LandedCostLine.create({
                    'cost_id': landed_cost.id,
                    'product_id': self._get_or_create_landed_cost_product('Transportation Cost').id,
                    'price_unit': transport_amount,
                    'split_method': 'by_po_line_cost',
                    'name': f'Transportation - Lot {lot.name}'
                })
                _logger.info(f"Transportation amount (9 decimals): {transport_amount}")

        # Thekedar Cost (based on calculated bag quantity)
        if order.x_thekedar_fees and adjusted_bag_quantity:
            thekedar_amount = round(adjusted_bag_quantity * order.x_thekedar_fees_value, 9)
            if thekedar_amount > 0:
                total_additional_cost += thekedar_amount
                LandedCostLine.create({
                    'cost_id': landed_cost.id,
                    'product_id': self._get_or_create_landed_cost_product('Thekedar Cost').id,
                    'price_unit': thekedar_amount,
                    'split_method': 'by_po_line_cost',
                    'name': f'Thekedar - Lot {lot.name}'
                })
                _logger.info(f"Thekedar amount (9 decimals): {thekedar_amount}")

        # Bag Cost (based on calculated bag quantity)
        if purchase_line.x_bag and adjusted_bag_quantity:
            bag_amount = round(adjusted_bag_quantity * purchase_line.x_bag.standard_price, 9)
            if bag_amount > 0:
                total_additional_cost += bag_amount
                LandedCostLine.create({
                    'cost_id': landed_cost.id,
                    'product_id': self._get_or_create_landed_cost_product('Bag Cost').id,
                    'price_unit': bag_amount,
                    'split_method': 'by_po_line_cost',
                    'name': f'Bag Cost - Lot {lot.name}'
                })
                _logger.info(f"Bag cost amount (9 decimals): {bag_amount}")

        total_additional_cost = round(total_additional_cost, 9)
        _logger.info(f"Total additional costs (9 decimals) for lot {lot.name}: {total_additional_cost}")

        return landed_cost if landed_cost.cost_lines else False

    @api.model
    def _get_or_create_landed_cost_product(self, name):
        """Helper method to get or create landed cost products."""
        if not name:
            raise UserError(_('Product name cannot be empty'))
            
        Product = self.env['product.product']
        product = Product.search([
            ('name', '=', name),
            ('type', '=', 'service'),
            ('landed_cost_ok', '=', True)
        ], limit=1)
        
        if not product:
            try:
                product = Product.create({
                    'name': name,
                    'type': 'service',
                    'landed_cost_ok': True,
                    'standard_price': 0.0,
                })
            except Exception as e:
                raise UserError(_('Failed to create landed cost product: %s') % str(e))
        
        return product

    def _validate_fee_value(self, fee_value, fee_name):
        if fee_value < 0:
            raise UserError(_(f'{fee_name} cannot be negative'))
        return fee_value


class StockLandedCostLine(models.Model):
    _inherit = 'stock.landed.cost.lines'

    split_method = fields.Selection(
        selection=[
            ('equal', 'Equal'),
            ('by_quantity', 'By Quantity'),
            ('by_current_cost_price', 'By Current Cost'),
            ('by_weight', 'By Weight'),
            ('by_volume', 'By Volume'),
            ('by_po_line_cost', 'By Purchase Order Line Cost'),
        ],
    )
    
    # Override price fields with higher precision
    price_unit = fields.Float(
        'Cost', digits=(16, 9), 
        required=True, 
        help="Cost that will be added to the cost of the products")
    final_cost = fields.Float(
        'Final Cost', digits=(16, 9), 
        compute='_compute_final_cost', 
        store=True)
