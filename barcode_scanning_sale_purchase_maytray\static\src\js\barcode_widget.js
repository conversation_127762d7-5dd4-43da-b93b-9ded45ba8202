odoo.define('barcode_scanning_sale_purchase.barcode_widget', function (require) {
    "use strict";

    var AbstractField = require('web.AbstractField');
    var core = require('web.core');
    var field_registry = require('web.field_registry');
    var Dialog = require('web.Dialog');
    var _t = core._t;

    var BarcodeWidget = AbstractField.extend({
        template: 'BarcodeWidget',
        events: {
            'change input': '_onInputChange',
            'keydown input': '_onKeydown',
        },

        init: function () {
            this._super.apply(this, arguments);
            this.products = [];
        },

        _renderEdit: function () {
            this.$input = this.$('input');
            this.$input.val(this.value || '');
        },

        _renderReadonly: function () {
            this.$el.text(this.value || '');
        },

        _onInputChange: function (event) {
            var self = this;
            var value = this.$input.val();
            this._setValue(value);

            if (value) {
                // Search for products with this barcode or lot number
                this._rpc({
                    model: 'stock.move',
                    method: 'get_products_by_lot',
                    args: [this.res_id, value],
                    context: this.record.getContext(),
                }).then(function (productIds) {
                    if (productIds && productIds.length > 1) {
                        // If multiple products found, show selection dialog
                        self._showProductSelectionDialog(productIds, value);
                    }
                });
            }
        },

        _onKeydown: function (event) {
            if (event.which === 13) { // Enter key
                event.preventDefault();
                this._onInputChange(event);
            }
        },

        _showProductSelectionDialog: function (productIds, lotNumber) {
            var self = this;
            
            // Get product details
            this._rpc({
                model: 'product.product',
                method: 'search_read',
                args: [[['id', 'in', productIds]], ['id', 'name', 'default_code', 'barcode']],
                context: this.record.getContext(),
            }).then(function (products) {
                var $content = $('<div>').addClass('o_product_selection');
                $content.append($('<p>').text(_t('Multiple products found for lot number: ') + lotNumber));
                
                var $table = $('<table>').addClass('table table-sm');
                var $header = $('<thead>').append($('<tr>')
                    .append($('<th>').text(_t('Code')))
                    .append($('<th>').text(_t('Name')))
                    .append($('<th>').text(_t('Barcode')))
                    .append($('<th>').text(_t('Select')))
                );
                $table.append($header);
                
                var $body = $('<tbody>');
                _.each(products, function (product) {
                    var $row = $('<tr>');
                    $row.append($('<td>').text(product.default_code || ''));
                    $row.append($('<td>').text(product.name || ''));
                    $row.append($('<td>').text(product.barcode || ''));
                    
                    var $selectButton = $('<button>')
                        .addClass('btn btn-sm btn-primary')
                        .text(_t('Select'))
                        .on('click', function () {
                            self._selectProduct(product.id);
                            dialog.close();
                        });
                    
                    $row.append($('<td>').append($selectButton));
                    $body.append($row);
                });
                
                $table.append($body);
                $content.append($table);
                
                var dialog = new Dialog(self, {
                    title: _t('Select Product'),
                    $content: $content,
                    buttons: [{
                        text: _t('Cancel'),
                        close: true
                    }],
                    size: 'medium',
                });
                
                dialog.open();
            });
        },

        _selectProduct: function (productId) {
            // Set the product on the record
            this.trigger_up('field_changed', {
                dataPointID: this.dataPointID,
                changes: {
                    product_id: {id: productId},
                },
            });
        },
    });

    field_registry.add('barcode_widget', BarcodeWidget);

    return BarcodeWidget;
});
