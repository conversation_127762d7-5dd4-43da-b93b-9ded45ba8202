from odoo import models, fields, api, _
from odoo.exceptions import UserError
from odoo.tools import float_round, float_compare
import logging

# Set up logger with appropriate level
_logger = logging.getLogger(__name__)
class MrpProduction(models.Model):
    _inherit = 'mrp.production'

    report_number = fields.Char('Report Number', help='Report Number')
    # Weight Fields
    total_raw_material_weight = fields.Float('Total Raw Material Weight', compute='_compute_finished_weights', store=True)
    total_other_material_weight = fields.Float('Total Other Material Weight', compute='_compute_finished_weights', store=True)
    total_weight = fields.Float('Total Weight', compute='_compute_finished_weights', store=True, readonly=True)
    raw_material_weight_percentage = fields.Float('Raw Material Weight %', compute='_compute_finished_weights', store=True)
    other_material_weight_percentage = fields.Float('Other Material Weight %', compute='_compute_finished_weights', store=True)

    # Finished Product Weight Fields
    finished_goods_weight = fields.Float('Finished Goods Weight', compute='_compute_product_amounts' , store=True)
    reclean_goods_weight = fields.Float('Reclean Goods Weight', compute='_compute_product_amounts', store=True)
    crushing_goods_weight = fields.Float('Crushing Goods Weight', compute='_compute_product_amounts', store=True)
    wastage_goods_weight = fields.Float('Wastage Goods Weight', compute='_compute_product_amounts', store=True)
    missing_weight = fields.Float('Missing Weight', compute='_compute_finished_weights', store=True)
    total_finished_weight = fields.Float('Total Finished Weight', compute='_compute_finished_weights' , store=True)

    # Raw Material Costs
    kachi_raw_material_amount = fields.Float(string='Kachi Raw Material Amount', compute='_compute_material_costs', store=True)
    other_material_amount = fields.Float(string='Other Material Amount', compute='_compute_material_costs', store=True)
    total_material_cost = fields.Float(string='Total Material Cost', compute='_compute_material_costs', store=True)

    # Additional Costs
    hamali_cost = fields.Float(string='Hamali Cost', default=0.0 )
    sortex_landed_cost = fields.Float(string='Sortex Landed Cost', default=0.0)
    total_bag_cost = fields.Float(string='Total Bag Cost', default=0.0)
    total_additional_cost = fields.Float(string='Total Additional Cost', compute='_compute_additional_cost', store=True)

    # Final Calculations
    total_cost_with_kachi = fields.Float(string='Total Cost with Kachi', compute='_compute_final_costs', store=True)
    ci_landed_cost = fields.Float(string='CI Landed Cost', default=0.0)
    raw_material_grand_amount = fields.Float(string='Raw Material Grand Amount', compute='_compute_final_costs', store=True)
    per_kg_cost = fields.Float(string='RAW Material Per KG Cost', compute='_compute_final_costs', store=True)
    final_raw_material_cost = fields.Float(string='Final Raw Material Cost', compute='_compute_final_costs', store=True)
    total_final_cost = fields.Float(string='Total Final Cost', compute='_compute_final_costs', store=True)



    # Add these new fields
    finished_goods_amount = fields.Float(string='Finished Goods Amount', compute='_compute_product_amounts' , store=True )
    reclean_goods_amount = fields.Float(string='Reclean Goods Amount', compute='_compute_product_amounts', store=True )
    crushing_goods_amount = fields.Float(string='Crushing Goods Amount', compute='_compute_product_amounts', store=True )
    wastage_goods_amount = fields.Float(string='Wastage Goods Amount', compute='_compute_product_amounts', store=True )
    total_amount = fields.Float(string='Total Amount', compute='_compute_product_amounts', store=True)

    # Percentage fields for finished amounts
    finished_goods_percentage = fields.Float('Finished Goods Percentage', compute='_compute_finished_percentages', store=True)
    reclean_goods_percentage = fields.Float('Reclean Goods Percentage', compute='_compute_finished_percentages', store=True)
    crushing_goods_percentage = fields.Float('Crushing Goods Percentage', compute='_compute_finished_percentages', store=True)
    wastage_goods_percentage = fields.Float('Wastage Goods Percentage', compute='_compute_finished_percentages', store=True)
    missing_weight_percentage = fields.Float('Missing Weight Percentage', compute='_compute_finished_percentages', store=True)

    # Add missing field declaration if not already present
    total_sales_amount = fields.Float(string='Total Sales Amount', compute='_compute_sales_amount', store=True)
    total_production_cost = fields.Float(string='Total Production Cost', compute='_compute_production_cost', store=True)
    profit_loss = fields.Float(string='Profit/Loss', compute='_compute_profit_loss', store=True)
    recommended_price = fields.Float(string='Recommended Price', compute='_compute_recommended_price', store=True)

    # Add these new fields
    cost_share_finished = fields.Float('Finished Goods Cost Share %', compute='_compute_cost_share_percentages', store=True)
    cost_share_reclean = fields.Float('Reclean Goods Cost Share %', compute='_compute_cost_share_percentages', store=True)
    cost_share_crushing = fields.Float('Crushing Goods Cost Share %', compute='_compute_cost_share_percentages', store=True)
    cost_share_wastage = fields.Float('Wastage Goods Cost Share %', compute='_compute_cost_share_percentages', store=True)
    total_cost_share = fields.Float('Total Cost Share %', compute='_compute_cost_share_percentages', store=True)

    # Add these missing field declarations
    cost_per_kg = fields.Float(string='Cost per KG', compute='_compute_profit_loss', store=True)
    break_even_price = fields.Float(string='Break Even Price', compute='_compute_profit_loss', store=True)

    def write(self, vals):
        """Override write to track changes to total_weight."""
        if 'total_weight' in vals:
            import traceback
            import inspect
            import sys

            # Get detailed stack trace
            stack = traceback.extract_stack()

            # Log the full stack trace with file names and line numbers
            stack_info = []
            for frame in stack:
                stack_info.append(f"File: {frame.filename}, Line: {frame.lineno}, Function: {frame.name}, Code: {frame.line}")

            # Get the current frame and its caller frames
            current_frame = inspect.currentframe()
            caller_frames = inspect.getouterframes(current_frame)

            # Log detailed information about caller frames
            caller_info = []
            for frame_info in caller_frames:
                frame = frame_info.frame
                code = frame_info.code_context[0].strip() if frame_info.code_context else "Unknown"
                caller_info.append(f"File: {frame_info.filename}, Line: {frame_info.lineno}, Function: {frame_info.function}, Code: {code}")

                # Log local variables in the frame
                if frame.f_locals:
                    local_vars = []
                    for key, value in frame.f_locals.items():
                        if key != 'self' and not key.startswith('__'):
                            try:
                                local_vars.append(f"{key} = {value}")
                            except:
                                local_vars.append(f"{key} = <unprintable>")
                    if local_vars:
                        caller_info.append(f"  Local variables: {', '.join(local_vars)}")

            for record in self:
                _logger.warning('WRITE: TOTAL WEIGHT BEING MODIFIED: %s -> %s (raw: %s, other: %s)',
                              record.total_weight, vals['total_weight'],
                              record.total_raw_material_weight,
                              record.total_other_material_weight)

                # Log the detailed stack information - focus on Odoo-specific frames
                for i, info in enumerate(stack_info):
                    if '/odoo/' in info or '/addons/' in info:
                        _logger.warning('IMPORTANT STACK FRAME %d: %s', i, info)

                # Log the detailed caller information - focus on Odoo-specific frames
                for i, info in enumerate(caller_info):
                    if '/odoo/' in info or '/addons/' in info:
                        _logger.warning('IMPORTANT CALLER FRAME %d: %s', i, info)

                # Check if the new value is correct
                correct_total = record.total_raw_material_weight + record.total_other_material_weight

                # Special check for the specific problematic value
                if vals['total_weight'] == 20718.25:
                    _logger.warning('FOUND THE PROBLEMATIC VALUE 20718.25!')

                # Check if the value is raw_material_weight + 2*other_material_weight
                double_other = record.total_raw_material_weight + (2 * record.total_other_material_weight)
                if abs(vals['total_weight'] - double_other) < 0.01:
                    _logger.warning('FOUND THE PATTERN! The value %s is approximately equal to raw_material_weight (%s) + 2*other_material_weight (%s) = %s',
                                  vals['total_weight'],
                                  record.total_raw_material_weight,
                                  record.total_other_material_weight,
                                  double_other)

                if vals['total_weight'] != correct_total:
                    _logger.warning('WRITE: CORRECTING TOTAL WEIGHT: %s -> %s', vals['total_weight'], correct_total)
                    vals['total_weight'] = correct_total

        return super(MrpProduction, self).write(vals)

    @api.onchange('is_production_b')
    def _onchange_is_production_b(self):
        for record in self:
            if record.is_production_b:
                record.sortex_landed_cost = 0.0
                record.ci_landed_cost = 0.0

    def action_update_raw_material_rate(self):
        """Update the rate of a raw material in a completed manufacturing order."""
        self.ensure_one()

        _logger.info("=== Starting rate update for MO %s ===", self.name)

        try:
            if self.state != 'done':
                _logger.warning("Cannot update rate: MO %s is not in 'done' state (current state: %s)", self.name, self.state)
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Warning'),
                        'message': _('Rate can only be updated for completed manufacturing orders.'),
                        'sticky': False,
                        'type': 'warning',
                    }
                }

            # Find the raw material with lot J231C25KU
            target_move = False
            lot_name = 'J231C25KU'

            _logger.info("Searching for lot %s in raw materials of MO %s", lot_name, self.name)

            for move in self.move_raw_ids:
                _logger.debug("Checking move ID %s, product %s", move.id, move.product_id.name)
                for move_line in move.move_line_ids:
                    if move_line.lot_id and move_line.lot_id.name == lot_name:
                        target_move = move
                        _logger.info("Found lot %s in raw materials move: %s (product: %s)",
                                    lot_name, move.id, move.product_id.name)
                        break
                if target_move:
                    break

            if not target_move:
                _logger.warning("Lot %s not found in raw materials of MO %s", lot_name, self.name)
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Error'),
                        'message': _(f'Lot {lot_name} not found in raw materials of this manufacturing order.'),
                        'sticky': False,
                        'type': 'danger',
                    }
                }

            # Store old values for logging
            old_rate = target_move.actual_cost
            old_price_unit = target_move.price_unit

            _logger.info("Current values for move %s - actual_cost: %s, price_unit: %s",
                        target_move.id, old_rate, old_price_unit)

            # Update the rate
            new_rate = 209.25
            _logger.info("Updating actual_cost from %s to %s for move %s",
                        old_rate, new_rate, target_move.id)

            try:
                target_move.write({
                    'actual_cost': new_rate,
                    'user_modified_rate': True
                })
                _logger.info("Successfully updated actual_cost to %s for move %s",
                            new_rate, target_move.id)
            except Exception as e:
                _logger.error("Error updating actual_cost: %s", str(e), exc_info=True)
                raise

            # Update price_unit based on the new rate
            if target_move.product_id and target_move.product_id.weight > 0:
                new_price_unit = new_rate * target_move.product_id.weight
                _logger.info("Updating price_unit from %s to %s for move %s",
                            old_price_unit, new_price_unit, target_move.id)

                try:
                    target_move.write({
                        'price_unit': new_price_unit
                    })
                    _logger.info("Successfully updated price_unit to %s for move %s",
                                new_price_unit, target_move.id)
                except Exception as e:
                    _logger.error("Error updating price_unit: %s", str(e), exc_info=True)
                    raise
            else:
                _logger.warning("Cannot update price_unit: product weight is zero or product not set")

            # Update stock valuation layer if it exists
            svls = self.env['stock.valuation.layer'].search([('stock_move_id', '=', target_move.id)])
            _logger.info("Found %s stock valuation layers for move %s", len(svls), target_move.id)

            if svls:
                for svl in svls:
                    # Calculate new value based on new rate
                    quantity = svl.quantity
                    new_value = quantity * new_rate * target_move.product_id.weight
                    _logger.info("Updating SVL %s: unit_cost from %s to %s, value from %s to %s",
                                svl.id, svl.unit_cost, new_rate, svl.value, new_value)

                    try:
                        svl.write({
                            'unit_cost': new_rate,
                            'value': new_value
                        })
                        _logger.info("Successfully updated SVL %s", svl.id)
                    except Exception as e:
                        _logger.error("Error updating stock valuation layer: %s", str(e), exc_info=True)
                        raise

            # Force recalculation of dependent fields
            _logger.info("Recalculating dependent fields")

            if hasattr(target_move, '_compute_total_cost'):
                try:
                    _logger.info("Calling _compute_total_cost on move %s", target_move.id)
                    target_move._compute_total_cost()
                except Exception as e:
                    _logger.error("Error in _compute_total_cost: %s", str(e), exc_info=True)
                    # Continue despite error

            # Recalculate manufacturing order costs
            # Calculate raw material cost
            try:
                _logger.info("Recalculating raw_material_cost for MO %s", self.id)
                raw_material_cost = sum(
                    move.product_uom_qty * move.actual_cost * move.product_id.weight
                    for move in self.move_raw_ids.filtered(lambda m: m.state == 'done')
                )
                _logger.info("New raw_material_cost: %s (old: %s)",
                            raw_material_cost, self.raw_material_cost)

                self.write({'raw_material_cost': raw_material_cost})
            except Exception as e:
                _logger.error("Error updating raw_material_cost: %s", str(e), exc_info=True)
                raise

            # Recalculate other costs
            if hasattr(self, '_compute_material_costs'):
                try:
                    _logger.info("Calling _compute_material_costs on MO %s", self.id)
                    self._compute_material_costs()
                except Exception as e:
                    _logger.error("Error in _compute_material_costs: %s", str(e), exc_info=True)
                    # Continue despite error

            if hasattr(self, '_compute_final_costs'):
                try:
                    _logger.info("Calling _compute_final_costs on MO %s", self.id)
                    self._compute_final_costs()
                except Exception as e:
                    _logger.error("Error in _compute_final_costs: %s", str(e), exc_info=True)
                    # Continue despite error

            if hasattr(self, '_compute_profit_loss'):
                try:
                    _logger.info("Calling _compute_profit_loss on MO %s", self.id)
                    self._compute_profit_loss()
                except Exception as e:
                    _logger.error("Error in _compute_profit_loss: %s", str(e), exc_info=True)
                    # Continue despite error

            # Refresh the records to ensure we have the latest values
            # Note: invalidate_cache is not available on stock.move, using env.invalidate_all() instead
            self.env.invalidate_all()

            _logger.info("=== Rate update completed successfully for MO %s ===", self.name)

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Success'),
                    'message': _(f'Rate updated from {old_rate} to {new_rate}'),
                    'sticky': False,
                    'type': 'success',
                }
            }

        except Exception as e:
            _logger.error("Unexpected error in action_update_raw_material_rate: %s", str(e), exc_info=True)
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Error'),
                    'message': _(f'An error occurred: {str(e)}'),
                    'sticky': True,
                    'type': 'danger',
                }
            }

    def reset_cost_analysis_fields(self):
        """Reset all cost analysis fields to zero when an Unbuild operation is performed."""
        self.ensure_one()

        _logger.info("=== Resetting cost analysis fields for MO %s due to Unbuild ===", self.name)

        # Create a dictionary with all cost analysis fields set to 0.0
        reset_vals = {
            # Weight Fields
            'total_raw_material_weight': 0.0,
            'total_other_material_weight': 0.0,
            'total_weight': 0.0,
            'raw_material_weight_percentage': 0.0,
            'other_material_weight_percentage': 0.0,

            # Finished Product Weight Fields
            'finished_goods_weight': 0.0,
            'reclean_goods_weight': 0.0,
            'crushing_goods_weight': 0.0,
            'wastage_goods_weight': 0.0,
            'missing_weight': 0.0,
            'total_finished_weight': 0.0,

            # Raw Material Costs
            'kachi_raw_material_amount': 0.0,
            'other_material_amount': 0.0,
            'total_material_cost': 0.0,

            # Additional Costs (only reset calculated fields, not user-entered fields)
            'total_additional_cost': 0.0,

            # Final Calculations
            'total_cost_with_kachi': 0.0,
            'raw_material_grand_amount': 0.0,
            'per_kg_cost': 0.0,
            'final_raw_material_cost': 0.0,
            'total_final_cost': 0.0,

            # Product Amounts
            'finished_goods_amount': 0.0,
            'reclean_goods_amount': 0.0,
            'crushing_goods_amount': 0.0,
            'wastage_goods_amount': 0.0,
            'total_amount': 0.0,

            # Percentage fields
            'finished_goods_percentage': 0.0,
            'reclean_goods_percentage': 0.0,
            'crushing_goods_percentage': 0.0,
            'wastage_goods_percentage': 0.0,
            'missing_weight_percentage': 0.0,

            # Cost Share Percentages
            'cost_share_finished': 0.0,
            'cost_share_reclean': 0.0,
            'cost_share_crushing': 0.0,
            'cost_share_wastage': 0.0,
            'total_cost_share': 0.0,

            # Other calculated fields
            'total_sales_amount': 0.0,
            'total_production_cost': 0.0,
            'profit_loss': 0.0,
            'recommended_price': 0.0,
            'cost_per_kg': 0.0,
            'break_even_price': 0.0,
            'profit_loss': 0.0
        }

        # Update the record with all fields set to 0.0
        self.write(reset_vals)

        _logger.info("=== Cost analysis fields reset successfully for MO %s ===", self.name)
        return True

    # def action_update_other_material_rate(self):
        """Update the rate of a specific other material in a completed manufacturing order."""
        self.ensure_one()

        _logger.info("=== Starting other material rate update for MO %s ===", self.name)

        try:
            if self.state != 'done':
                _logger.warning("Cannot update rate: MO %s is not in 'done' state (current state: %s)", self.name, self.state)
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Warning'),
                        'message': _('Rate can only be updated for completed manufacturing orders.'),
                        'sticky': False,
                        'type': 'warning',
                    }
                }

            # Find the other material with lot MJB31C25OC and ID 1712
            target_move = False
            lot_name = 'MJB31C25OC'
            move_id = 1712

            _logger.info("Searching for other material with ID %s and lot %s in MO %s", move_id, lot_name, self.name)

            # First try to find by ID
            target_move = self.env['stock.move'].browse(move_id)
            if not target_move.exists() or target_move.state != 'done':
                _logger.warning("Move with ID %s not found or not in 'done' state", move_id)
                target_move = False
            else:
                _logger.info("Found move with ID %s: %s", move_id, target_move.product_id.name)

                # Verify it's part of this MO's other materials
                if target_move not in self.move_other_ids:
                    _logger.warning("Move with ID %s is not part of MO %s other materials", move_id, self.name)
                    target_move = False
                else:
                    # Verify it has the correct lot
                    has_correct_lot = False
                    for move_line in target_move.move_line_ids:
                        if move_line.lot_id and move_line.lot_id.name == lot_name:
                            has_correct_lot = True
                            _logger.info("Confirmed lot %s in move ID %s", lot_name, move_id)
                            break

                    if not has_correct_lot:
                        _logger.warning("Move with ID %s does not have lot %s", move_id, lot_name)
                        target_move = False

            # If not found by ID, try to find by lot
            if not target_move:
                for move in self.move_other_ids:
                    _logger.debug("Checking other material move ID %s, product %s", move.id, move.product_id.name)
                    for move_line in move.move_line_ids:
                        if move_line.lot_id and move_line.lot_id.name == lot_name:
                            target_move = move
                            _logger.info("Found lot %s in other materials move: %s (product: %s)",
                                        lot_name, move.id, move.product_id.name)
                            break
                    if target_move:
                        break

            if not target_move:
                _logger.warning("Other material with lot %s not found in MO %s", lot_name, self.name)
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Error'),
                        'message': _(f'Other material with lot {lot_name} not found in this manufacturing order.'),
                        'sticky': False,
                        'type': 'danger',
                    }
                }

            # Store old values for logging
            old_rate = target_move.actual_cost
            old_price_unit = target_move.price_unit

            _logger.info("Current values for move %s - actual_cost: %s, price_unit: %s",
                        target_move.id, old_rate, old_price_unit)

            # Update the rate
            new_rate = 450.0
            _logger.info("Updating actual_cost from %s to %s for move %s",
                        old_rate, new_rate, target_move.id)

            try:
                target_move.write({
                    'actual_cost': new_rate,
                    'user_modified_rate': True
                })
                _logger.info("Successfully updated actual_cost to %s for move %s",
                            new_rate, target_move.id)
            except Exception as e:
                _logger.error("Error updating actual_cost: %s", str(e), exc_info=True)
                raise

            # Update price_unit based on the new rate
            if target_move.product_id and target_move.product_id.weight > 0:
                new_price_unit = new_rate * target_move.product_id.weight
                _logger.info("Updating price_unit from %s to %s for move %s",
                            old_price_unit, new_price_unit, target_move.id)

                try:
                    target_move.write({
                        'price_unit': new_price_unit
                    })
                    _logger.info("Successfully updated price_unit to %s for move %s",
                                new_price_unit, target_move.id)
                except Exception as e:
                    _logger.error("Error updating price_unit: %s", str(e), exc_info=True)
                    raise
            else:
                _logger.warning("Cannot update price_unit: product weight is zero or product not set")

            # Update stock valuation layer if it exists
            svls = self.env['stock.valuation.layer'].search([('stock_move_id', '=', target_move.id)])
            _logger.info("Found %s stock valuation layers for move %s", len(svls), target_move.id)

            if svls:
                for svl in svls:
                    # Calculate new value based on new rate
                    quantity = svl.quantity
                    new_value = quantity * new_rate * target_move.product_id.weight
                    _logger.info("Updating SVL %s: unit_cost from %s to %s, value from %s to %s",
                                svl.id, svl.unit_cost, new_rate, svl.value, new_value)

                    try:
                        svl.write({
                            'unit_cost': new_rate,
                            'value': new_value
                        })
                        _logger.info("Successfully updated SVL %s", svl.id)
                    except Exception as e:
                        _logger.error("Error updating stock valuation layer: %s", str(e), exc_info=True)
                        raise

            # Force recalculation of dependent fields
            _logger.info("Recalculating dependent fields")

            if hasattr(target_move, '_compute_total_cost'):
                try:
                    _logger.info("Calling _compute_total_cost on move %s", target_move.id)
                    target_move._compute_total_cost()
                except Exception as e:
                    _logger.error("Error in _compute_total_cost: %s", str(e), exc_info=True)
                    # Continue despite error

            # Recalculate manufacturing order costs
            # Calculate other material cost
            try:
                _logger.info("Recalculating other_material_amount for MO %s", self.id)
                other_material_amount = sum(
                    move.product_uom_qty * move.actual_cost * move.product_id.weight
                    for move in self.move_other_ids.filtered(lambda m: m.state == 'done')
                )
                _logger.info("New other_material_amount: %s (old: %s)",
                            other_material_amount, self.other_material_amount)

                self.write({'other_material_amount': other_material_amount})
            except Exception as e:
                _logger.error("Error updating other_material_amount: %s", str(e), exc_info=True)
                raise

            # Recalculate other costs
            if hasattr(self, '_compute_material_costs'):
                try:
                    _logger.info("Calling _compute_material_costs on MO %s", self.id)
                    self._compute_material_costs()
                except Exception as e:
                    _logger.error("Error in _compute_material_costs: %s", str(e), exc_info=True)
                    # Continue despite error

            if hasattr(self, '_compute_final_costs'):
                try:
                    _logger.info("Calling _compute_final_costs on MO %s", self.id)
                    self._compute_final_costs()
                except Exception as e:
                    _logger.error("Error in _compute_final_costs: %s", str(e), exc_info=True)
                    # Continue despite error

            if hasattr(self, '_compute_profit_loss'):
                try:
                    _logger.info("Calling _compute_profit_loss on MO %s", self.id)
                    self._compute_profit_loss()
                except Exception as e:
                    _logger.error("Error in _compute_profit_loss: %s", str(e), exc_info=True)
                    # Continue despite error

            # Force recalculation of product amounts
            if hasattr(self, '_compute_product_amounts'):
                try:
                    _logger.info("Calling _compute_product_amounts on MO %s", self.id)
                    self._compute_product_amounts()
                except Exception as e:
                    _logger.error("Error in _compute_product_amounts: %s", str(e), exc_info=True)
                    # Continue despite error

            # Refresh the records to ensure we have the latest values
            self.env.invalidate_all()

            _logger.info("=== Other material rate update completed successfully for MO %s ===", self.name)

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Success'),
                    'message': _(f'Other material rate updated from {old_rate} to {new_rate}'),
                    'sticky': False,
                    'type': 'success',
                }
            }

        except Exception as e:
            _logger.error("Unexpected error in action_update_other_material_rate: %s", str(e), exc_info=True)
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Error'),
                    'message': _(f'An error occurred: {str(e)}'),
                    'sticky': True,
                    'type': 'danger',
                }
            }

    @api.depends('sortex_landed_cost', 'ci_landed_cost', 'is_production_b')
    def _compute_sortex_and_ci_landed_cost(self):
        for record in self:
            if record.is_production_b:
                record.sortex_landed_cost = 0.0
                record.ci_landed_cost = 0.0
            else:
                # Place your original calculation here if any
                pass

    @api.depends('hamali_cost', 'sortex_landed_cost', 'total_bag_cost')
    def _compute_additional_cost(self):
        for record in self:
            if not record.id:
                continue
            record.total_additional_cost = (
                record.hamali_cost +
                record.sortex_landed_cost +
                record.total_bag_cost
            )

    @api.depends(
        'move_raw_ids.product_uom_qty',  # Add direct dependency on move_raw_ids
        'move_raw_ids.state',
        'move_raw_material_ids.product_uom_qty',
        'move_raw_material_ids.state',
        'move_raw_material_ids.product_id.weight',
        'move_other_ids.product_uom_qty',
        'move_other_ids.state',
        'move_other_ids.product_id.weight',
        'move_finished_ids.product_uom_qty',
        'move_finished_ids.state',
        'move_finished_ids.product_id.weight',
        'move_finished_ids.x_product_total_weight',
        'move_finished_ids.sale_product',
        'move_finished_ids.riclin_product',
        'move_finished_ids.clean_product',
        'move_finished_ids.waste_product'
    )
    def _compute_finished_weights(self):
        if _logger.isEnabledFor(logging.DEBUG):
            _logger.debug('_compute_finished_weights triggered')
        for record in self:
            # Log initial values before any changes only in debug mode
            if _logger.isEnabledFor(logging.DEBUG):
                _logger.debug('Initial values - MO %s: raw=%s, other=%s, total=%s',
                            record.name, record.total_raw_material_weight, record.total_other_material_weight, record.total_weight)

            # Initialize all weight fields to 0.0 to ensure they're always set
            record.total_raw_material_weight = 0.0
            record.total_other_material_weight = 0.0
            record.total_weight = 0.0
            record.raw_material_weight_percentage = 0.0
            record.other_material_weight_percentage = 0.0
            record.finished_goods_weight = 0.0
            record.reclean_goods_weight = 0.0
            record.crushing_goods_weight = 0.0
            record.wastage_goods_weight = 0.0
            record.missing_weight = 0.0
            record.total_finished_weight = 0.0

            if not record.id:
                continue

            _logger.info('Computing finished weights for MO: %s', record.name)
            finished_moves = record.move_finished_ids.filtered(lambda m: m.state != 'draft')

            # Log all raw material moves
            _logger.info('Raw material moves:')
            raw_material_moves = record.move_raw_material_ids.filtered(lambda m: m.state not in ('cancel', 'draft'))
            raw_material_ids = raw_material_moves.ids
            for move in raw_material_moves:
                _logger.info('Raw Material: %s, ID: %s, Qty: %s, Weight/Unit: %s, Total: %s',
                            move.product_id.name, move.id, move.product_uom_qty,
                            move.product_id.weight,
                            move.product_uom_qty * move.product_id.weight)

            raw_weight = sum(
                move.product_uom_qty * move.product_id.weight
                for move in raw_material_moves
            )

            # Log all other material moves
            _logger.info('Other material moves:')
            other_material_moves = record.move_other_ids.filtered(lambda m: m.state not in ('cancel', 'draft'))
            other_material_ids = other_material_moves.ids
            for move in other_material_moves:
                _logger.info('Other Material: %s, ID: %s, Qty: %s, Weight/Unit: %s, Total: %s',
                            move.product_id.name, move.id, move.product_uom_qty,
                            move.product_id.weight,
                            move.product_uom_qty * move.product_id.weight)

            # Check for overlap between raw_material_ids and other_material_ids
            overlap_ids = set(raw_material_ids).intersection(set(other_material_ids))
            if overlap_ids:
                _logger.warning('OVERLAP DETECTED: Some moves are in both raw_material_ids and other_material_ids: %s', list(overlap_ids))

            # Calculate total weight from other materials
            other_weight = sum(
                move.product_uom_qty * move.product_id.weight
                for move in other_material_moves
            )


            # Calculate materials weights
            _logger.info('WEIGHT CALCULATION - BEFORE: raw_weight=%s, other_weight=%s, total_weight=%s',
                        raw_weight, other_weight, record.total_weight)

            record.total_raw_material_weight = raw_weight
            record.total_other_material_weight = other_weight
            record.total_weight = record.total_raw_material_weight + record.total_other_material_weight

            _logger.info('WEIGHT CALCULATION - AFTER: total_raw_material_weight=%s, total_other_material_weight=%s, total_weight=%s',
                        record.total_raw_material_weight, record.total_other_material_weight, record.total_weight)

            _logger.info('********************Just Calculated Raw Weight %s',str(raw_weight))
            _logger.info('Just Calculated Other Weight %s',str(other_weight))
            _logger.info('Just Updated Total Weight %s',str(record.total_weight))

            # Calculate weight percentages
            if record.total_weight:
                record.raw_material_weight_percentage = (record.total_raw_material_weight / record.total_weight) * 100
                record.other_material_weight_percentage = (record.total_other_material_weight / record.total_weight) * 100
                _logger.info('Weight percentages - Raw: %s%%, Other: %s%%',
                            record.raw_material_weight_percentage,
                            record.other_material_weight_percentage)
            else:
                record.raw_material_weight_percentage = 0.0
                record.other_material_weight_percentage = 0.0
                _logger.warning('Total weight is zero, setting percentages to 0')

            # Calculate finished product weights
            finished_sale_moves = finished_moves.filtered(lambda m: m.sale_product)
            finished_riclin_moves = finished_moves.filtered(lambda m: m.riclin_product)
            finished_clean_moves = finished_moves.filtered(lambda m: m.clean_product)
            finished_waste_moves = finished_moves.filtered(lambda m: m.waste_product)

            # Log finished goods
            record.finished_goods_weight = sum(move.x_product_total_weight for move in finished_sale_moves)
            for move in finished_sale_moves:
                _logger.info('Finished Good: %s, Weight: %s',
                            move.product_id.name, move.x_product_total_weight)

            # Log reclean goods
            record.reclean_goods_weight = sum(move.x_product_total_weight for move in finished_riclin_moves)
            for move in finished_riclin_moves:
                _logger.info('Reclean Good: %s, Weight: %s',
                            move.product_id.name, move.x_product_total_weight)

            # Log crushing goods
            record.crushing_goods_weight = sum(move.x_product_total_weight for move in finished_clean_moves)
            for move in finished_clean_moves:
                _logger.info('Crushing Good: %s, Weight: %s',
                            move.product_id.name, move.x_product_total_weight)

            # Log wastage goods
            record.wastage_goods_weight = sum(move.x_product_total_weight for move in finished_waste_moves)
            for move in finished_waste_moves:
                _logger.info('Wastage Good: %s, Weight: %s',
                            move.product_id.name, move.x_product_total_weight)

            _logger.info('Summary of finished weights - Finished: %s, Reclean: %s, Crushing: %s, Wastage: %s',
                        record.finished_goods_weight,
                        record.reclean_goods_weight,
                        record.crushing_goods_weight,
                        record.wastage_goods_weight)

            # Calculate total finished weight (sum of all output weights)
            record.total_finished_weight = (
                record.finished_goods_weight +
                record.reclean_goods_weight +
                record.crushing_goods_weight +
                record.wastage_goods_weight
            )
            _logger.info('Total finished weight: %s', record.total_finished_weight)

            # Calculate missing weight (difference between output and input weights)
            # Positive value means more output than input (gain)
            # Negative value means less output than input (loss)
            record.missing_weight = record.total_finished_weight - record.total_weight
            _logger.info('Missing weight: %s', record.missing_weight)

    @api.depends('move_raw_ids', 'move_finished_ids')
    def _compute_quantities(self):
        for record in self:
            if not record.id:
                continue
            # Available quantity from finished moves
            finished_moves = record.move_finished_ids.filtered(lambda m: m.state == 'done')
            record.available_quantity = sum(finished_moves.mapped('product_uom_qty'))

            # Sold quantity from related sale moves
            sold_moves = finished_moves.filtered(lambda m: m.sale_product)
            record.sold_quantity = sum(sold_moves.mapped('product_uom_qty'))

            # Calculate remaining stock
            record.remaining_stock = record.available_quantity - record.sold_quantity

            # Calculate total quantity from byproducts
            sale_moves = record.move_byproduct_ids.filtered(lambda m: m.sale_product)
            record.total_quantity = sum(move.product_uom_qty * move.product_id.weight
                                     for move in sale_moves)

    @api.depends('raw_material_cost', 'other_materials_cost',
             'move_raw_ids', 'move_raw_ids.product_uom_qty', 'move_raw_ids.state',
             'move_other_ids', 'move_other_ids.product_uom_qty', 'move_other_ids.state',
             'move_other_ids.lot_ids', 'move_other_ids.actual_cost', 'move_other_ids.total_cost')
    def _compute_material_costs(self):
        # Add a context flag to prevent recursive calls
        if self.env.context.get('skip_material_costs_computation'):
            _logger.warning('MATERIAL_COSTS_DEBUG: Skipping _compute_material_costs due to context flag')
            return

        _logger.warning('MATERIAL_COSTS_DEBUG: _compute_material_costs triggered')

        # Log the context to see if this is being called from a form save
        _logger.warning('MATERIAL_COSTS_DEBUG: Context: %s', self.env.context)

        # Get the caller information if possible
        import inspect
        import traceback
        stack = traceback.extract_stack()
        _logger.warning('MATERIAL_COSTS_DEBUG: Called from: %s', stack[-2][0:3])

        # Use a savepoint to prevent database changes if something goes wrong
        with self.env.cr.savepoint():
            for record in self:
                if not record.id:
                    continue

                _logger.warning('MATERIAL_COSTS_DEBUG: Computing material costs for MO: %s (ID: %s)', record.name, record.id)
                _logger.warning('MATERIAL_COSTS_DEBUG: Initial values - Raw Material Cost: %s, Other Materials Cost: %s, other_material_amount: %s',
                             record.raw_material_cost, record.other_materials_cost, record.other_material_amount)

                # Calculate Kachi Raw Material Amount
                record.kachi_raw_material_amount = record.raw_material_cost
                _logger.warning('MATERIAL_COSTS_DEBUG: Set kachi_raw_material_amount = %s', record.kachi_raw_material_amount)

                # Calculate Other Material Amount directly from the sum of total_cost
                # This ensures it's always calculated the same way as in the wizard

                # IMPORTANT FIX: Conditional filtering based on MO state
                # If MO is in 'done' state, only consider 'done' stock moves
                # Otherwise, consider all stock moves that are not cancelled or draft
                if record.state == 'done':
                    # For completed MOs, only consider 'done' stock moves
                    other_moves = record.move_other_ids.filtered(lambda m: m.state == 'done')
                    _logger.warning('MATERIAL_COSTS_DEBUG: MO is in "done" state - Found %s other material moves with state "done"', len(other_moves))
                else:
                    # For MOs in other states, consider all relevant stock moves
                    other_moves = record.move_other_ids.filtered(lambda m: m.state not in ['cancel', 'draft'])
                    _logger.warning('MATERIAL_COSTS_DEBUG: MO is in "%s" state - Found %s other material moves with state not in ["cancel", "draft"]',
                                  record.state, len(other_moves))

                # Also log the total number of other material moves for comparison
                all_other_moves = record.move_other_ids
                _logger.warning('MATERIAL_COSTS_DEBUG: Total other material moves: %s (including all states)', len(all_other_moves))

                # Log the states of all other material moves
                states = all_other_moves.mapped('state')
                _logger.warning('MATERIAL_COSTS_DEBUG: States of all other material moves: %s', states)

                # Log each move's details before any recalculation
                _logger.warning("MATERIAL_COSTS_DEBUG: Other material moves before recalculation:")
                for m in other_moves:
                    lot_info = []
                    for lot in m.lot_ids:
                        lot_info.append(f"{lot.name}(avg_cost_per_weight={lot.avg_cost_per_weight})")

                    _logger.warning("MATERIAL_COSTS_DEBUG: Move ID: %s, Product: %s, actual_cost: %s, product_uom_qty: %s, weight: %s, total_cost: %s, lot_ids: %s",
                                  m.id, m.product_id.name, m.actual_cost, m.product_uom_qty,
                                  m.product_id.weight, m.total_cost,
                                  lot_info if lot_info else m.lot_ids.mapped('name'))

                # First, check if any moves have lot_ids but no actual_cost or total_cost
                for m in other_moves:
                    if m.lot_ids and (m.actual_cost <= 0 or m.total_cost <= 0):
                        _logger.warning("MATERIAL_COSTS_DEBUG: CRITICAL - Move %s has lot_ids but actual_cost=%s, total_cost=%s",
                                      m.id, m.actual_cost, m.total_cost)

                        # Call the recalculate_costs_from_lots method if available
                        if hasattr(m, 'recalculate_costs_from_lots'):
                            _logger.warning("MATERIAL_COSTS_DEBUG: Calling recalculate_costs_from_lots for move %s", m.id)
                            # Use with_context to prevent recursive calls
                            m.with_context(skip_material_costs_computation=True).recalculate_costs_from_lots()
                            _logger.warning("MATERIAL_COSTS_DEBUG: After recalculate_costs_from_lots - move %s actual_cost: %s, total_cost: %s",
                                          m.id, m.actual_cost, m.total_cost)
                        else:
                            # Otherwise, force recalculation of actual_cost and total_cost
                            _logger.warning("MATERIAL_COSTS_DEBUG: Move %s has lot_ids, forcing recalculation of actual_cost and total_cost", m.id)
                            # Invalidate the cache for these fields to force recalculation
                            m.invalidate_recordset(['actual_cost', 'total_cost'])
                            _logger.warning("MATERIAL_COSTS_DEBUG: After invalidate - move %s actual_cost: %s, total_cost: %s",
                                          m.id, m.actual_cost, m.total_cost)

                # Now process all moves
                for m in other_moves:
                    # Force recalculation of actual_cost and total_cost for each move
                    # This ensures lot-based costs are properly reflected
                    if m.lot_ids:
                        _logger.warning("MATERIAL_COSTS_DEBUG: Move %s has lot_ids, forcing recalculation of actual_cost and total_cost", m.id)
                        # Store old values for comparison
                        old_actual_cost = m.actual_cost
                        old_total_cost = m.total_cost

                        # Invalidate the cache for these fields to force recalculation
                        m.invalidate_recordset(['actual_cost', 'total_cost'])

                        # Log the changes
                        _logger.warning("MATERIAL_COSTS_DEBUG: Move %s costs updated - actual_cost: %s -> %s, total_cost: %s -> %s",
                                      m.id, old_actual_cost, m.actual_cost, old_total_cost, m.total_cost)

                    # Log detailed information about the move and its lots
                    lot_info = []
                    for lot in m.lot_ids:
                        lot_info.append(f"{lot.name}(avg_cost_per_weight={lot.avg_cost_per_weight})")

                    _logger.warning("MATERIAL_COSTS_DEBUG: After processing - Move ID: %s, Product: %s, actual_cost: %s, product_uom_qty: %s, weight: %s, total_cost: %s, lot_ids: %s",
                                  m.id, m.product_id.name, m.actual_cost, m.product_uom_qty, m.product_id.weight, m.total_cost, lot_info if lot_info else m.lot_ids.mapped('name'))

                # Calculate the expected total_cost for each move to verify
                for m in other_moves:
                    expected_total_cost = m.actual_cost * (m.product_uom_qty * m.product_id.weight)
                    if abs(expected_total_cost - m.total_cost) > 0.01:
                        _logger.warning("MATERIAL_COSTS_DEBUG: Move %s total_cost (%s) doesn't match expected value (%s) - actual_cost: %s, product_uom_qty: %s, weight: %s",
                                      m.id, m.total_cost, expected_total_cost, m.actual_cost, m.product_uom_qty, m.product_id.weight)

                # Store the old value for comparison
                old_other_material_amount = record.other_material_amount
                old_other_materials_cost = record.other_materials_cost

                # Ensure we have the latest total_cost values
                _logger.warning("MATERIAL_COSTS_DEBUG: Invalidating cache to ensure fresh values")
                record.env.invalidate_all()

                # Recalculate the sum with fresh values
                other_materials_sum = sum(m.total_cost for m in other_moves)

                _logger.warning("MATERIAL_COSTS_DEBUG: Calculated other_material_amount from total_cost sum: %s (previous: %s, other_materials_cost: %s) for MO %s",
                              other_materials_sum, record.other_material_amount, record.other_materials_cost, record.name)

                # Always use the sum of total_cost for other_material_amount
                record.other_material_amount = other_materials_sum
                _logger.warning("MATERIAL_COSTS_DEBUG: Set other_material_amount = %s", other_materials_sum)

                # Also update other_materials_cost to match
                if record.other_materials_cost != other_materials_sum:
                    _logger.warning("MATERIAL_COSTS_DEBUG: Updating other_materials_cost to match total_cost sum: %s -> %s for MO %s",
                                  record.other_materials_cost, other_materials_sum, record.name)
                    record.other_materials_cost = other_materials_sum

                # Calculate Total Material Cost
                record.total_material_cost = record.kachi_raw_material_amount + record.other_material_amount
                _logger.warning("MATERIAL_COSTS_DEBUG: Set total_material_cost = %s + %s = %s",
                              record.kachi_raw_material_amount, record.other_material_amount, record.total_material_cost)

                # Log the final values
                _logger.warning('MATERIAL_COSTS_DEBUG: Final values - Kachi: %s, Other: %s (changed from %s), Total: %s',
                             record.kachi_raw_material_amount,
                             record.other_material_amount,
                             old_other_material_amount,
                             record.total_material_cost)

                # Log all other material moves one more time to verify they haven't changed
                _logger.warning("MATERIAL_COSTS_DEBUG: Final verification of other material moves:")
                for m in other_moves:
                    _logger.warning("MATERIAL_COSTS_DEBUG: Final - Move ID: %s, Product: %s, actual_cost: %s, total_cost: %s",
                                  m.id, m.product_id.name, m.actual_cost, m.total_cost)

    @api.depends('total_material_cost', 'total_additional_cost')
    def _compute_production_cost(self):
        for record in self:
            if not record.id:
                continue
            record.total_production_cost = record.total_material_cost + record.total_additional_cost


    @api.depends('move_finished_ids.party_moves.total_amount')
    def _compute_sales_amount(self):
        for record in self:
            if not record.id:
                continue

            _logger.info('Computing total sales amount for MO: %s', record.name)

            # Get all finished moves for this specific MRP order
            finished_moves = record.move_finished_ids.filtered(
                lambda m: m.state != 'cancel' and
                         m.production_id.id == record.id
            )

            total = 0.0
            for move in finished_moves:
                move_total = sum(party_move.total_amount for party_move in move.party_moves)
                _logger.info('Product: %s, Total Amount: %s',
                            move.product_id.name, move_total)
                total += move_total

            record.total_sales_amount = total
            _logger.info('Total sales amount for MO %s: %s',
                        record.name, total)

    @api.depends(
        'total_sales_amount',
    #     'total_final_cost',
    #     'reclean_goods_amount',
    #     'crushing_goods_amount',
    #     'wastage_goods_amount',
    #     'finished_goods_weight',
    #     'move_finished_ids.product_uom_qty',
    #     'move_finished_ids.state',
    #     'move_finished_ids.product_id.weight',
    #     'move_finished_ids.sale_product',
    #     'move_finished_ids.party_moves',
    #     'move_finished_ids.party_moves.weight_sold',
    #     'move_finished_ids.party_moves.total_amount',
    #     'move_byproduct_ids.product_uom_qty',
    #     'move_byproduct_ids.state',
    #     'move_byproduct_ids.product_id.weight',
    #     'total_amount'
    )
    def _compute_profit_loss(self):
        if _logger.isEnabledFor(logging.DEBUG):
            _logger.debug('_compute_profit_loss triggered')
        for record in self:
            if _logger.isEnabledFor(logging.DEBUG):
                _logger.debug('Profit/Loss - Initial values - MO %s: raw=%s, other=%s, total=%s',
                            record.name, record.total_raw_material_weight, record.total_other_material_weight, record.total_weight)

            # Fix for total_weight calculation - ensure it's the sum of raw and other material weights
            if record.total_weight != record.total_raw_material_weight + record.total_other_material_weight:
                _logger.warning('FIXING TOTAL WEIGHT IN PROFIT/LOSS: Current value %s is incorrect. Setting to %s (raw: %s + other: %s)',
                              record.total_weight,
                              record.total_raw_material_weight + record.total_other_material_weight,
                              record.total_raw_material_weight,
                              record.total_other_material_weight)
                record.total_weight = record.total_raw_material_weight + record.total_other_material_weight

            if not record.id:
                continue

            _logger.info('Computing profit/loss for MO: %s', str(record.id))

            # Calculate total weight sold from party moves
            finished_moves = record.move_finished_ids.filtered(
                lambda m: m.state == 'done' and m.sale_product
            )

            weight_sold = sum(
                sum(party_move.weight_sold for party_move in move.party_moves)
                for move in finished_moves
            )

            _logger.info('Detailed weight calculation:')
            for move in finished_moves:
                move_weight = sum(party_move.weight_sold for party_move in move.party_moves)
                _logger.info('Move: %s , Party Moves Total Weight: %s', move.product_id.name, move_weight)

            # Ensure weight_sold is properly rounded
            weight_sold = float_round(weight_sold, precision_digits=3)

            _logger.info('---Computing profit/loss for MO: %s', str(record.id))
            _logger.info('PROFIT/LOSS - WEIGHTS: total_raw_material_weight=%s, total_other_material_weight=%s, total_weight=%s',
                        record.total_raw_material_weight, record.total_other_material_weight, record.total_weight)

            # Calculate remaining weight to be sold (using exact weights)
            remaining_weight = float_round(record.finished_goods_weight - weight_sold, precision_digits=3)
            _logger.info('---Total Finished Goods Weight for MO: %s', str(record.finished_goods_weight))

            # Calculate base cost per kg with zero division check
            if record.finished_goods_weight and float_compare(record.finished_goods_weight, 0.001, precision_digits=3) > 0:
                # Use finished_goods_weight instead of total_weight for more accurate cost calculation
                cost_per_kg = record.total_final_cost / record.finished_goods_weight
            else:
                cost_per_kg = 0.0
                _logger.warning('Finished goods weight is zero or too small for MO %s, setting cost_per_kg to 0', record.name)

            # Calculate break-even selling price (price needed for zero profit/loss)
            break_even_price = cost_per_kg

            # Calculate actual profit/loss based on current selling price
            if record.total_sales_amount > 0:
                actual_profit_loss = record.total_sales_amount - record.total_final_cost
            else:
                actual_profit_loss = record.total_amount - record.total_final_cost

            # Set recommended price slightly above break-even (e.g., 1% margin)
            recommended_price = break_even_price * 1.01

            # Store the computed values
            record.cost_per_kg = cost_per_kg
            record.profit_loss = actual_profit_loss
            record.recommended_price = recommended_price
            record.break_even_price = break_even_price

            _logger.info('Profit/Loss calculation details:')
            _logger.info('Total Sales Amount: %s', record.total_sales_amount)
            _logger.info('Total Final Cost: %s', record.total_final_cost)
            _logger.info('Total Weight: %s', record.finished_goods_weight)
            _logger.info('Weight Sold (from party moves): %s', weight_sold)
            _logger.info('Remaining Weight: %s', remaining_weight)
            _logger.info('Cost per KG: %s', cost_per_kg)
            _logger.info('Recommended Price: %s', recommended_price)
            _logger.info('Profit/Loss: %s', record.profit_loss)

            # Final fix for total_weight calculation in profit/loss - ensure it's still the sum of raw and other material weights
            if record.total_weight != record.total_raw_material_weight + record.total_other_material_weight:
                _logger.warning('FINAL FIX FOR TOTAL WEIGHT IN PROFIT/LOSS: Current value %s is incorrect. Setting to %s (raw: %s + other: %s)',
                              record.total_weight,
                              record.total_raw_material_weight + record.total_other_material_weight,
                              record.total_raw_material_weight,
                              record.total_other_material_weight)
                record.total_weight = record.total_raw_material_weight + record.total_other_material_weight

            _logger.info('FINAL TOTAL WEIGHT IN PROFIT/LOSS: %s (raw: %s + other: %s)',
                        record.total_weight, record.total_raw_material_weight, record.total_other_material_weight)

    @api.depends('kachi_raw_material_amount', 'total_additional_cost', 'ci_landed_cost', 'move_raw_material_ids.product_uom_qty', 'move_other_ids.product_uom_qty', 'total_raw_material_weight')
    def _compute_final_costs(self):
        if _logger.isEnabledFor(logging.DEBUG):
            _logger.debug('_compute_final_costs triggered')
        for record in self:
            if _logger.isEnabledFor(logging.DEBUG):
                _logger.debug('Final costs - Initial values - MO %s: raw=%s, other=%s, total=%s',
                            record.name, record.total_raw_material_weight, record.total_other_material_weight, record.total_weight)

            # Fix for total_weight calculation - ensure it's the sum of raw and other material weights
            if record.total_weight != record.total_raw_material_weight + record.total_other_material_weight:
                _logger.warning('FIXING TOTAL WEIGHT: Current value %s is incorrect. Setting to %s (raw: %s + other: %s)',
                              record.total_weight,
                              record.total_raw_material_weight + record.total_other_material_weight,
                              record.total_raw_material_weight,
                              record.total_other_material_weight)
                record.total_weight = record.total_raw_material_weight + record.total_other_material_weight

            if not record.id:
                continue

            _logger.info('Computing final costs for MO: %s', record.name)

            # Calculate Total Cost with Kachi
            record.total_cost_with_kachi = record.kachi_raw_material_amount +  record.total_additional_cost
            _logger.info('Total Cost with Kachi: %s (Material Cost: %s, Additional Cost: %s)',
                        record.total_cost_with_kachi, record.kachi_raw_material_amount, record.total_additional_cost)

            # Calculate Raw Material Grand Amount
            record.raw_material_grand_amount = record.total_cost_with_kachi + record.ci_landed_cost
            _logger.info('Raw Material Grand Amount: %s (Total Cost with Kachi: %s, CI Landed Cost: %s)',
                        record.raw_material_grand_amount, record.total_cost_with_kachi, record.ci_landed_cost)

            # Use the already calculated total_raw_material_weight instead of recalculating
            # This prevents double-counting of other materials

            # Fix for total_weight calculation again - ensure it's still the sum of raw and other material weights
            # This is needed because something might be resetting the value
            if record.total_weight != record.total_raw_material_weight + record.total_other_material_weight:
                _logger.warning('FIXING TOTAL WEIGHT AGAIN: Current value %s is incorrect. Setting to %s (raw: %s + other: %s)',
                              record.total_weight,
                              record.total_raw_material_weight + record.total_other_material_weight,
                              record.total_raw_material_weight,
                              record.total_other_material_weight)
                record.total_weight = record.total_raw_material_weight + record.total_other_material_weight

            _logger.info('FINAL COSTS - WEIGHTS: total_raw_material_weight=%s, total_other_material_weight=%s, total_weight=%s',
                        record.total_raw_material_weight, record.total_other_material_weight, record.total_weight)

            # Log individual raw material move weights
            for move in record.move_raw_material_ids.filtered(lambda m: m.state not in ('cancel', 'draft')):
                if move.product_id and move.product_id.weight:
                    _logger.info('Raw Material Weight Detail - Product: %s, Qty: %s, Weight/Unit: %s, Total: %s',
                                move.product_id.name, move.product_uom_qty,
                                move.product_id.weight,
                                move.product_uom_qty * move.product_id.weight)

            # Fix for total_weight calculation one more time - ensure it's still the sum of raw and other material weights
            # This is needed because something might be resetting the value
            if record.total_weight != record.total_raw_material_weight + record.total_other_material_weight:
                _logger.warning('FIXING TOTAL WEIGHT BEFORE PER KG COST: Current value %s is incorrect. Setting to %s (raw: %s + other: %s)',
                              record.total_weight,
                              record.total_raw_material_weight + record.total_other_material_weight,
                              record.total_raw_material_weight,
                              record.total_other_material_weight)
                record.total_weight = record.total_raw_material_weight + record.total_other_material_weight

            # Calculate Per KG Cost with zero division check - use only raw material weight
            # This is the key fix - we're using only raw_material_weight for per_kg_cost calculation
            # not total_weight which would include other_material_weight
            if record.total_raw_material_weight and float_compare(record.total_raw_material_weight, 0.001, precision_digits=3) > 0:
                record.per_kg_cost = record.raw_material_grand_amount / record.total_raw_material_weight
                if _logger.isEnabledFor(logging.DEBUG):
                    _logger.debug('Using raw_material_weight (%s) for per_kg_cost calculation, not total_weight (%s)',
                               record.total_raw_material_weight, record.total_weight)
            else:
                record.per_kg_cost = 0.0
                _logger.warning('Total raw material weight is zero or too small for MO %s, setting per_kg_cost to 0', record.name)

            _logger.info('Per KG Cost: %s (Raw Material Grand Amount: %s / total_raw_material_weight: %s)',
                        record.per_kg_cost, record.raw_material_grand_amount, record.total_raw_material_weight)

            # Set Final Raw Material Cost
            record.final_raw_material_cost = record.raw_material_grand_amount
            _logger.info('Final Raw Material Cost: %s', record.final_raw_material_cost)

            # Calculate Total Final Cost
            record.total_final_cost = record.final_raw_material_cost + record.other_material_amount
            _logger.info('Total Final Cost: %s', record.total_final_cost)

            # Final fix for total_weight calculation - ensure it's still the sum of raw and other material weights
            # This is needed because something might be resetting the value
            if record.total_weight != record.total_raw_material_weight + record.total_other_material_weight:
                _logger.warning('FINAL FIX FOR TOTAL WEIGHT: Current value %s is incorrect. Setting to %s (raw: %s + other: %s)',
                              record.total_weight,
                              record.total_raw_material_weight + record.total_other_material_weight,
                              record.total_raw_material_weight,
                              record.total_other_material_weight)
                record.total_weight = record.total_raw_material_weight + record.total_other_material_weight

            _logger.info('FINAL TOTAL WEIGHT: %s (raw: %s + other: %s)',
                        record.total_weight, record.total_raw_material_weight, record.total_other_material_weight)

    @api.constrains('move_finished_ids')
    def _check_byproducts(self):
        _logger.info('_check_byproducts TRIGGERED')
        for record in self:
            _logger.info('BYPRODUCTS - INITIAL VALUES - MO %s: total_raw_material_weight=%s, total_other_material_weight=%s, total_weight=%s',
                        record.name, record.total_raw_material_weight, record.total_other_material_weight, record.total_weight)

            # Fix for total_weight calculation - ensure it's the sum of raw and other material weights
            if record.total_weight != record.total_raw_material_weight + record.total_other_material_weight:
                _logger.warning('FIXING TOTAL WEIGHT IN BYPRODUCTS: Current value %s is incorrect. Setting to %s (raw: %s + other: %s)',
                              record.total_weight,
                              record.total_raw_material_weight + record.total_other_material_weight,
                              record.total_raw_material_weight,
                              record.total_other_material_weight)
                record.total_weight = record.total_raw_material_weight + record.total_other_material_weight
        _logger.info("Final cost share distribution before validation:")
        total_cost_share = 0

        for byproduct in self.move_byproduct_ids.filtered(lambda m: m.state not in ('cancel')):
            _logger.info("Byproduct: %s, Cost Share: %f%%", byproduct.product_id.name, byproduct.cost_share)
            total_cost_share += byproduct.cost_share

        total_cost_share = round(total_cost_share, 6)
        # Calculate difference from 100% for logging purposes
        total_cost_share1 = round(total_cost_share - 100, 6)
        _logger.info("Total cost share: %f%%, Difference from 100%%: %f%%", total_cost_share, total_cost_share1)

        if total_cost_share > 100.000000:
            _logger.error("Total by-product cost share exceeds 100%%: %f%%", total_cost_share)
            raise UserError(_("Total by-product cost share exceeds 100%%! Check by-product cost distribution."))






    # @api.depends(
    #     'move_byproduct_ids.product_uom_qty',
    #     'move_byproduct_ids.by_product_rate',
    #     'move_byproduct_ids.product_id.weight',
    #     'move_byproduct_ids.state',
    #     'move_byproduct_ids.sale_product',
    #     'move_byproduct_ids.riclin_product',
    #     'move_byproduct_ids.clean_product',
    #     'move_byproduct_ids.waste_product'
    # )
    @api.depends('move_byproduct_ids', 'move_byproduct_ids.product_uom_qty',
              'move_byproduct_ids.by_product_rate', 'move_byproduct_ids.product_id.weight',
              'move_byproduct_ids.state', 'move_byproduct_ids.sale_product',
              'move_byproduct_ids.riclin_product', 'move_byproduct_ids.clean_product',
              'move_byproduct_ids.waste_product', 'is_production_b')
    def _compute_product_amounts(self):
        _logger = logging.getLogger(__name__)
        for record in self:
            # Initialize all fields to 0.0 first
            record.finished_goods_amount = 0.0
            record.reclean_goods_amount = 0.0
            record.crushing_goods_amount = 0.0
            record.wastage_goods_amount = 0.0
            record.total_amount = 0.0
            record.finished_goods_weight = 0.0
            record.reclean_goods_weight = 0.0
            record.crushing_goods_weight = 0.0
            record.wastage_goods_weight = 0.0

            # Skip computation for new records
            if not record.id:
                continue

            # We don't need to skip calculations for Production type B
            # Special handling for Production type B
            # if record.is_production_b:
            #     _logger.info('Production type B detected for MO %s - skipping product amount calculations', record.name)
            #     # For Production type B, we don't calculate product amounts
            #     # This is because Production type B only includes hamali_cost and total_bag_cost
            #     continue

            # The byproduct rates and amounts should still be calculated correctly

            _logger.info('Computing product amounts for MO: %s (ID: %s)', record.name, record.id)

            # Get all byproducts first for logging
            all_byproducts = record.move_byproduct_ids
            _logger.info('Total byproducts before filtering: %d', len(all_byproducts))

            # Log details for each byproduct before filtering
            for bp in all_byproducts:
                _logger.info('Before filtering - Byproduct: %s (ID: %s), State: %s, Type: %s%s%s%s, Qty: %s, Weight: %s, Rate: %s',
                           bp.product_id.name, bp.id, bp.state,
                           'Sale ' if bp.sale_product else '',
                           'Riclin ' if bp.riclin_product else '',
                           'Clean ' if bp.clean_product else '',
                           'Waste ' if bp.waste_product else '',
                           bp.product_uom_qty, bp.product_id.weight,
                           bp.by_product_rate)

            # IMPORTANT CHANGE: Include draft byproducts in the calculation
            # Only filter out cancelled byproducts and the main product
            active_byproducts = record.move_byproduct_ids.filtered(
                lambda m: m.state != 'cancel' and
                          m.product_id.id != record.product_id.id
            )

            _logger.info('Total byproducts after filtering: %d', len(active_byproducts))

            # Log the byproducts we're processing
            for move in active_byproducts:
                _logger.info('Processing byproduct: %s, Qty: %s, Weight: %s, Rate: %s, Sale: %s, Riclin: %s, Clean: %s, Waste: %s',
                           move.product_id.name, move.product_uom_qty, move.product_id.weight,
                           move.by_product_rate, move.sale_product, move.riclin_product,
                           move.clean_product, move.waste_product)

            for move in active_byproducts:
                # Calculate total weight
                total_weight = move.product_uom_qty * (move.product_id.weight or 0.0)
                # Calculate amount using weight and rate
                amount = total_weight * (move.by_product_rate or 0.0)

                _logger.info('Byproduct calculation: %s - Weight: %s, Rate: %s, Amount: %s',
                           move.product_id.name, total_weight, move.by_product_rate, amount)

                # Categorize based on product type
                if move.sale_product:
                    record.finished_goods_amount += amount
                    record.finished_goods_weight += total_weight
                    _logger.info('Added to finished_goods: Amount: %s, Weight: %s', amount, total_weight)
                elif move.riclin_product:
                    record.reclean_goods_amount += amount
                    record.reclean_goods_weight += total_weight
                    _logger.info('Added to reclean_goods: Amount: %s, Weight: %s', amount, total_weight)
                elif move.clean_product:
                    record.crushing_goods_amount += amount
                    record.crushing_goods_weight += total_weight
                    _logger.info('Added to crushing_goods: Amount: %s, Weight: %s', amount, total_weight)
                elif move.waste_product:
                    record.wastage_goods_amount += amount
                    record.wastage_goods_weight += total_weight
                    _logger.info('Added to wastage_goods: Amount: %s, Weight: %s', amount, total_weight)

            record.total_amount = (record.finished_goods_amount + record.reclean_goods_amount +
                                 record.crushing_goods_amount + record.wastage_goods_amount)

            _logger.info('Final product amounts for MO %s: Finished: %s, Reclean: %s, Crushing: %s, Wastage: %s, Total: %s',
                       record.name, record.finished_goods_amount, record.reclean_goods_amount,
                       record.crushing_goods_amount, record.wastage_goods_amount, record.total_amount)

    # @api.depends('total_weight', 'finished_goods_weight', 'reclean_goods_weight',
    #             'crushing_goods_weight', 'wastage_goods_weight', 'missing_weight')
    def _compute_finished_percentages(self):
        for record in self:
            # Initialize all percentage fields to 0.0
            record.finished_goods_percentage = 0.0
            record.reclean_goods_percentage = 0.0
            record.crushing_goods_percentage = 0.0
            record.wastage_goods_percentage = 0.0
            record.missing_weight_percentage = 0.0

            # Skip computation for new records
            if not record.id:
                continue

            # Fix for total_weight calculation - ensure it's the sum of raw and other material weights
            if record.total_weight != record.total_raw_material_weight + record.total_other_material_weight:
                _logger.warning('FIXING TOTAL WEIGHT IN PERCENTAGES: Current value %s is incorrect. Setting to %s (raw: %s + other: %s)',
                              record.total_weight,
                              record.total_raw_material_weight + record.total_other_material_weight,
                              record.total_raw_material_weight,
                              record.total_other_material_weight)
                record.total_weight = record.total_raw_material_weight + record.total_other_material_weight

            # Calculate percentages based on total_weight for input percentages
            if record.total_weight and float_compare(record.total_weight, 0.001, precision_digits=3) > 0:
                # Calculate output percentages relative to total input weight
                record.finished_goods_percentage = (record.finished_goods_weight / record.total_weight) * 100
                record.reclean_goods_percentage = (record.reclean_goods_weight / record.total_weight) * 100
                record.crushing_goods_percentage = (record.crushing_goods_weight / record.total_weight) * 100
                record.wastage_goods_percentage = (record.wastage_goods_weight / record.total_weight) * 100

                # Missing weight percentage is the percentage difference between output and input
                record.missing_weight_percentage = (record.missing_weight / record.total_weight) * 100

                _logger.info('Percentages calculated based on total input weight: %s', record.total_weight)
            else:
                _logger.warning('Total weight is zero or too small for MO %s, cannot calculate percentages', record.name)

            _logger.info("Percentages for MO %s:", record.name)
            _logger.info("Finished Goods: %s%%", record.finished_goods_percentage)
            _logger.info("Reclean Goods: %s%%", record.reclean_goods_percentage)
            _logger.info("Crushing Goods: %s%%", record.crushing_goods_percentage)
            _logger.info("Wastage Goods: %s%%", record.wastage_goods_percentage)
            _logger.info("Missing Weight: %s%%", record.missing_weight_percentage)


    # @api.depends('move_finished_ids')
    # def _compute_waste_product_ids(self):
    #     for record in self:
    #         record.waste_product_ids = record.move_finished_ids.filtered(
    #             lambda m: m.state == 'done' and m.waste_product
    #         )

    # @api.depends('waste_product_ids', 'waste_product_ids.total_cost')
    # def _compute_waste_costs(self):
    #     for record in self:
    #         record.waste_product_cost = sum(record.waste_product_ids.mapped('total_cost') or [0.0])

    # @api.depends('move_byproduct_ids', 'move_byproduct_ids.cost_share')
    def _compute_cost_share_percentages(self):
        for record in self:
            # Initialize values
            record.cost_share_finished = 0.0
            record.cost_share_reclean = 0.0
            record.cost_share_crushing = 0.0
            record.cost_share_wastage = 0.0
            record.total_cost_share = 0.0

            if not record.id:
                continue

            for move in record.move_byproduct_ids.filtered(lambda m: m.state not in ('cancel')):
                if move.sale_product:
                    record.cost_share_finished += move.cost_share
                elif move.riclin_product:
                    record.cost_share_reclean += move.cost_share
                elif move.clean_product:
                    record.cost_share_crushing += move.cost_share
                elif move.waste_product:
                    record.cost_share_wastage += move.cost_share

            record.total_cost_share = (
                record.cost_share_finished +
                record.cost_share_reclean +
                record.cost_share_crushing +
                record.cost_share_wastage
            )

            _logger.info('Cost Share Percentages for MO %s:', record.name)
            _logger.info('Finished: %s%%', record.cost_share_finished)
            _logger.info('Reclean: %s%%', record.cost_share_reclean)
            _logger.info('Crushing: %s%%', record.cost_share_crushing)
            _logger.info('Wastage: %s%%', record.cost_share_wastage)
            _logger.info('Total: %s%%', record.total_cost_share)



from odoo import models, fields, api

class ManufacturingCostReport(models.Model):
    _name = 'manufacturing.cost.report'
    _description = 'Manufacturing Cost Details Report'
    _inherit = 'mrp.production'  # Inherit from mrp.production instead of creating a virtual model

    # No need to redefine fields that already exist in mrp.production

    # Add any additional fields or calculations you need
    warehouse_id = fields.Many2one('stock.warehouse', string="Warehouse", compute="_compute_warehouse", store=False)

    @api.depends('location_dest_id')
    def _compute_warehouse(self):
        for record in self:
            record.warehouse_id = record.location_dest_id.get_warehouse() if record.location_dest_id else False



from odoo import models, fields, api, tools

class ManufacturingCostReport(models.Model):
    _name = 'manufacturing.cost.report'
    _description = 'Manufacturing Cost Details Report'
    _auto = False  # This is a SQL view
    _rec_name = 'production_id'

    production_id = fields.Many2one('mrp.production', string='Manufacturing Order', readonly=True)
    product_id = fields.Many2one('product.product', string='Product', readonly=True)
    hamali_cost = fields.Float(string='Hamali Cost', readonly=True)
    sortex_landed_cost = fields.Float(string='Sortex Landed Cost', readonly=True)
    ci_landed_cost = fields.Float(string='CI Landed Cost', readonly=True)
    total_bag_cost = fields.Float(string='Total Bag Cost', readonly=True)
    date = fields.Datetime(string='Date', readonly=True)
    profit_loss = fields.Float(string='Profit / Loss', readonly=True)
    warehouse_id = fields.Many2one('stock.warehouse', string="Warehouse", readonly=True)

    def init(self):
        tools.drop_view_if_exists(self.env.cr, self._table)
        self.env.cr.execute("""
            CREATE OR REPLACE VIEW %s AS (
                SELECT
                    mp.id AS id,
                    mp.id AS production_id,
                    mp.product_id AS product_id,
                    mp.hamali_cost AS hamali_cost,
                    mp.sortex_landed_cost AS sortex_landed_cost,
                    mp.ci_landed_cost AS ci_landed_cost,
                    mp.total_bag_cost AS total_bag_cost,
                    mp.create_date AS date,
                    mp.profit_loss AS profit_loss,
                    sw.id AS warehouse_id
                FROM mrp_production mp
                LEFT JOIN stock_location sl ON mp.location_dest_id = sl.id
                LEFT JOIN stock_warehouse sw ON sl.warehouse_id = sw.id
            )
        """ % self._table)