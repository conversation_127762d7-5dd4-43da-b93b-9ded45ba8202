from odoo import models, fields, api, _
from odoo.exceptions import UserError
import logging
from datetime import datetime

_logger = logging.getLogger(__name__)

class MrpProduction(models.Model):
    _inherit = 'mrp.production'

    can_edit_dates = fields.Boolean(
        compute='_compute_can_edit_dates',
        string='Can Edit Dates',
        help='Technical field to determine if the user can edit dates on confirmed manufacturing orders'
    )

    @api.depends('state')
    def _compute_can_edit_dates(self):
        """Determine if the user can edit dates on this manufacturing order."""
        is_admin_date_editor = self.env.user.has_group('ai_admin_date_edit.group_admin_date_edit')
        for production in self:
            # Allow editing dates if the user is in the admin date edit group and the MO is confirmed
            production.can_edit_dates = is_admin_date_editor and production.state in ['confirmed', 'progress', 'to_close', 'done']

    def action_edit_dates(self):
        """Open a wizard to edit dates on the manufacturing order."""
        self.ensure_one()
        if not self.can_edit_dates:
            raise UserError(_("You don't have permission to edit dates on this manufacturing order."))

        return {
            'name': _('Edit Dates'),
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'res_model': 'mrp.production',
            'res_id': self.id,
            'target': 'new',
            'context': {
                'edit_dates': True,
            },
        }

    def write(self, vals):
        """Override write to allow changing dates on confirmed manufacturing orders."""
        # Check if we're editing dates and if the user has permission
        is_admin_date_editor = self.env.user.has_group('ai_admin_date_edit.group_admin_date_edit')
        is_editing_dates = any(field in vals for field in ['date_planned_start', 'date_planned_finished', 'date_deadline'])

        # Store original values for later processing
        productions_with_date_changes = {}
        if is_editing_dates:
            for production in self:
                if production.state in ['confirmed', 'progress', 'to_close', 'done']:
                    changes = {}
                    if 'date_planned_start' in vals and production.date_planned_start != vals['date_planned_start']:
                        changes['date_planned_start'] = {'old': production.date_planned_start, 'new': vals['date_planned_start']}
                    if 'date_planned_finished' in vals and production.date_planned_finished != vals['date_planned_finished']:
                        changes['date_planned_finished'] = {'old': production.date_planned_finished, 'new': vals['date_planned_finished']}
                    if 'date_deadline' in vals and production.date_deadline != vals['date_deadline']:
                        changes['date_deadline'] = {'old': production.date_deadline, 'new': vals['date_deadline']}

                    if changes:
                        productions_with_date_changes[production.id] = changes

        # If editing dates on confirmed MOs, ensure the user has permission
        if is_editing_dates and any(production.state in ['confirmed', 'progress', 'to_close', 'done'] for production in self):
            if not is_admin_date_editor:
                raise UserError(_("You don't have permission to edit dates on confirmed manufacturing orders."))

            # Log the date changes for audit purposes
            for production in self:
                if production.state in ['confirmed', 'progress', 'to_close', 'done']:
                    changes = []
                    if 'date_planned_start' in vals and production.date_planned_start != vals['date_planned_start']:
                        changes.append(f"date_planned_start: {production.date_planned_start} -> {vals['date_planned_start']}")
                    if 'date_planned_finished' in vals and production.date_planned_finished != vals['date_planned_finished']:
                        changes.append(f"date_planned_finished: {production.date_planned_finished} -> {vals['date_planned_finished']}")
                    if 'date_deadline' in vals and production.date_deadline != vals['date_deadline']:
                        changes.append(f"date_deadline: {production.date_deadline} -> {vals['date_deadline']}")

                    if changes:
                        _logger.info(f"Admin {self.env.user.name} changed dates on manufacturing order {production.name}: {', '.join(changes)}")

                        # Update related stock moves if dates are changed
                        if ('date_planned_start' in vals or 'date_planned_finished' in vals) and production.move_raw_ids:
                            date_to_use = vals.get('date_planned_start', production.date_planned_start)
                            for move in production.move_raw_ids.filtered(lambda m: m.state not in ['done', 'cancel']):
                                move.date = date_to_use
                                _logger.info(f"Updated date on raw material move {move.id} to {date_to_use}")

                        if 'date_planned_finished' in vals and production.move_finished_ids:
                            for move in production.move_finished_ids.filtered(lambda m: m.state not in ['done', 'cancel']):
                                move.date = vals['date_planned_finished']
                                _logger.info(f"Updated date on finished product move {move.id} to {vals['date_planned_finished']}")

        # Proceed with the write operation
        result = super(MrpProduction, self).write(vals)

        # Update related records if dates were changed
        if productions_with_date_changes and is_admin_date_editor:
            self._update_related_records_after_date_change(productions_with_date_changes)

        return result

    def _update_related_records_after_date_change(self, productions_with_date_changes):
        """Update related records when dates are changed on a confirmed manufacturing order."""
        for production_id, changes in productions_with_date_changes.items():
            production = self.browse(production_id)

            # Log the changes
            change_descriptions = []
            for field, values in changes.items():
                change_descriptions.append(f"{field}: {values['old']} -> {values['new']}")

            _logger.info(f"Updating related records for manufacturing order {production.name} after date changes: {', '.join(change_descriptions)}")

            # 1. Update stock move dates for raw materials
            if 'date_planned_start' in changes and production.move_raw_ids:
                new_date = changes['date_planned_start']['new']
                valuation_layers_total = 0

                for move in production.move_raw_ids:
                    if move.state == 'done':
                        # For done moves, we need to update valuation layers and quants
                        valuation_layers = self.env['stock.valuation.layer'].search([
                            ('stock_move_id', '=', move.id)
                        ])
                        if valuation_layers:
                            valuation_layers.write({
                                'create_date': new_date,
                                'accounting_date': new_date,
                            })
                            valuation_layers_total += len(valuation_layers)

                        # Update quants for this move
                        quants = self.env['stock.quant'].search([
                            ('product_id', '=', move.product_id.id),
                            ('location_id', '=', move.location_dest_id.id),
                            ('in_date', '=', move.date)
                        ])
                        if quants:
                            quants.write({'in_date': new_date})
                            _logger.info(f"Updated in_date on {len(quants)} stock quants to {new_date}")
                    else:
                        # For moves not done, just update the date
                        move.write({'date': new_date})

                if valuation_layers_total > 0:
                    _logger.info(f"Updated dates on {valuation_layers_total} stock valuation layers for raw materials to {new_date}")

                _logger.info(f"Updated dates on {len(production.move_raw_ids)} raw material moves to {new_date}")

            # 2. Update stock move dates for finished products
            if 'date_planned_finished' in changes and production.move_finished_ids:
                new_date = changes['date_planned_finished']['new']
                valuation_layers_total = 0

                for move in production.move_finished_ids:
                    if move.state == 'done':
                        # For done moves, we need to update valuation layers and quants
                        valuation_layers = self.env['stock.valuation.layer'].search([
                            ('stock_move_id', '=', move.id)
                        ])
                        if valuation_layers:
                            valuation_layers.write({
                                'create_date': new_date,
                                'accounting_date': new_date,
                            })
                            valuation_layers_total += len(valuation_layers)

                        # Update quants for this move
                        quants = self.env['stock.quant'].search([
                            ('product_id', '=', move.product_id.id),
                            ('location_id', '=', move.location_dest_id.id),
                            ('in_date', '=', move.date)
                        ])
                        if quants:
                            quants.write({'in_date': new_date})
                            _logger.info(f"Updated in_date on {len(quants)} stock quants to {new_date}")
                    else:
                        # For moves not done, just update the date
                        move.write({'date': new_date})

                if valuation_layers_total > 0:
                    _logger.info(f"Updated dates on {valuation_layers_total} stock valuation layers for finished products to {new_date}")

                _logger.info(f"Updated dates on {len(production.move_finished_ids)} finished product moves to {new_date}")

            # 3. Update work orders if they exist
            if any(field in changes for field in ['date_planned_start', 'date_planned_finished']) and production.workorder_ids:
                for workorder in production.workorder_ids:
                    workorder_changes = {}
                    if 'date_planned_start' in changes and workorder.date_planned_start:
                        workorder_changes['date_planned_start'] = changes['date_planned_start']['new']
                    if 'date_planned_finished' in changes and workorder.date_planned_finished:
                        workorder_changes['date_planned_finished'] = changes['date_planned_finished']['new']

                    if workorder_changes:
                        workorder.write(workorder_changes)
                        _logger.info(f"Updated dates on work order {workorder.name} to {workorder_changes}")

            # 4. Update accounting entries related to this manufacturing order
            account_moves = self.env['account.move'].search([
                '|',
                ('ref', 'like', production.name),
                ('stock_move_id', 'in', (production.move_raw_ids + production.move_finished_ids).ids)
            ])

            if account_moves:
                new_date = changes.get('date_planned_finished', {}).get('new') or changes.get('date_planned_start', {}).get('new')
                for move in account_moves:
                    if move.state != 'posted':
                        move.write({'date': new_date})
                        _logger.info(f"Updated date on account move {move.name} to {new_date}")
                    else:
                        _logger.warning(f"Account move {move.name} is already posted and cannot be updated automatically")

            # 5. Update quality checks if they exist and are linked to this MO
            if hasattr(self.env, 'quality.check'):
                quality_checks = self.env['quality.check'].search([
                    ('production_id', '=', production.id)
                ])

                if quality_checks:
                    new_date = changes.get('date_planned_start', {}).get('new')
                    if new_date:
                        quality_checks.write({'date': new_date})
                        _logger.info(f"Updated date on {len(quality_checks)} quality checks to {new_date}")

            # 6. Update maintenance requests if they exist and are linked to this MO
            if hasattr(self.env, 'maintenance.request'):
                maintenance_requests = self.env['maintenance.request'].search([
                    ('production_id', '=', production.id)
                ])

                if maintenance_requests:
                    new_date = changes.get('date_planned_start', {}).get('new')
                    if new_date:
                        maintenance_requests.write({'schedule_date': new_date})
                        _logger.info(f"Updated schedule_date on {len(maintenance_requests)} maintenance requests to {new_date}")

            # 7. Update lot/serial numbers if they were created by this MO
            if production.state == 'done' and production.move_finished_ids:
                lots_updated = 0
                new_date = changes.get('date_planned_finished', {}).get('new')

                if new_date:
                    for move in production.move_finished_ids:
                        for move_line in move.move_line_ids:
                            if move_line.lot_id and move_line.lot_id.create_date == move.date:
                                move_line.lot_id.write({'create_date': new_date})
                                lots_updated += 1

                    if lots_updated > 0:
                        _logger.info(f"Updated create_date on {lots_updated} lot/serial numbers to {new_date}")

            # Add a note to the production for audit purposes
            production.message_post(
                body=_("Dates changed by %s. Related records have been updated accordingly: %s") %
                     (self.env.user.name, ', '.join(change_descriptions)),
                subtype_id=self.env.ref('mail.mt_note').id
            )
