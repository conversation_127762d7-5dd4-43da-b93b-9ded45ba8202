from odoo import models, fields, _
from odoo.exceptions import UserError
import re
import logging

_logger = logging.getLogger(__name__)


class PurchaseOrder(models.Model):
    _inherit = 'purchase.order'

    x_reference_product_id = fields.Many2one(
        'product.product',
        string='Reference Product',
        help='Select a reference product to create a variant with different weight. After selecting a reference product and entering a weight, click the button to create a new product variant and add it to the order lines.'
    )

    x_bharati_weight = fields.Float(
        string='Bharati (Weight)',
        help='Enter the weight for the new product variant.',
        digits=(16, 2)
    )

    def action_create_product_variant(self):
        """
        Create a new product variant based on the reference product with updated weight
        and naming convention: 'PRODUCT_NAME WEIGHT [PACKAGE_WEIGHT]'
        Then add it as a new purchase order line if it doesn't already exist in the order.
        """
        self.ensure_one()
        _logger.info("Starting product variant creation process from purchase order")

        try:
            if not self.x_reference_product_id:
                _logger.warning("No reference product selected")
                raise UserError(_("Please select a reference product first."))

            if not self.x_bharati_weight or self.x_bharati_weight <= 0:
                _logger.warning(f"Invalid weight value: {self.x_bharati_weight}")
                raise UserError(_("Please enter a valid weight greater than zero."))

            reference_product = self.x_reference_product_id
            _logger.info(f"Reference product selected: {reference_product.name} (ID: {reference_product.id})")

            # Format the weight for the product name
            # For the weight inside brackets, always use KG notation
            weight_str_kg = f"{str(self.x_bharati_weight)}KG"
            # For other parts of the name, use G for < 1000g and KG for >= 1000g
            weight_str = f"{str(self.x_bharati_weight)}KG" if self.x_bharati_weight < 0 else f"{str(self.x_bharati_weight)}KG"
            _logger.info(f"Formatted weight strings: bracket={weight_str_kg}, other={weight_str}")

            # Parse the reference product name to identify its components
            # Example: "AJMO DIAMOND 500G [30KG]"
            reference_name = reference_product.name
            _logger.info(f"Processing reference product name: {reference_name}")

            # Find the bracket pattern in the product name (e.g., "[30KG]")
            bracket_match = re.search(r'\[(.*?)\]', reference_name)

            if bracket_match:
                _logger.info(f"Bracket pattern found: {bracket_match.group(0)}")
                # Extract the content inside brackets (for logging purposes)
                _logger.info(f"Content inside brackets: {bracket_match.group(1)}")

                # Create the new bracket content with the new weight (always using KG notation)
                new_bracket_content = f"[{weight_str_kg}]"

                # Replace only the bracket part, keeping the rest of the name intact
                new_product_name = reference_name.replace(bracket_match.group(0), new_bracket_content)
                _logger.info(f"New product name after bracket replacement: {new_product_name}")
            else:
                _logger.info("No bracket pattern found in product name")
                # If no bracket pattern found, look for a weight pattern
                weight_pattern = re.search(r'(\d+(?:\.\d+)?[GM]G?)', reference_name)

                if weight_pattern:
                    _logger.info(f"Weight pattern found in name: {weight_pattern.group(0)}")
                    # Replace only the weight part, keeping the rest of the name intact
                    new_product_name = reference_name.replace(weight_pattern.group(0), weight_str)
                    _logger.info(f"New product name after weight replacement: {new_product_name}")
                else:
                    _logger.info("No weight pattern found either, appending weight to name")
                    # No weight part, just append the weight
                    new_product_name = f"{reference_name} {weight_str}"
                    _logger.info(f"New product name with appended weight: {new_product_name}")

            # Check if product with this name already exists
            existing_product = self.env['product.product'].search([
                ('name', '=', new_product_name)
            ], limit=1)

            if existing_product:
                _logger.info(f"Found existing product with name '{new_product_name}' (ID: {existing_product.id})")
                # Use the existing product
                new_product = existing_product
            else:
                _logger.info(f"Creating new product with name: {new_product_name}")
                # Create a new product based on the reference product
                # Convert to kg if weight is in grams (Odoo stores weight in kg)
                new_weight_kg = self.x_bharati_weight if self.x_bharati_weight >= 0 else self.x_bharati_weight
                new_default_code = f"{reference_product.default_code or ''}-{weight_str_kg}" if reference_product.default_code else None

                _logger.info(f"New product details - Weight: {new_weight_kg}kg, Default code: {new_default_code}")

                try:
                    # Prepare copy values with basic fields
                    copy_vals = {
                        'name': new_product_name,
                        'weight': new_weight_kg,
                        'default_code': new_default_code,
                    }

                    # Add custom fields from ai_bt_spices_module if they exist on the reference product
                    # Product template fields
                    # Explicitly set raw_material and other_material fields
                    copy_vals['raw_material'] = reference_product.raw_material if hasattr(reference_product, 'raw_material') else False
                    _logger.info(f"Explicitly setting raw_material: {reference_product.raw_material if hasattr(reference_product, 'raw_material') else False}")

                    copy_vals['other_material'] = reference_product.other_material if hasattr(reference_product, 'other_material') else False
                    _logger.info(f"Explicitly setting other_material: {reference_product.other_material if hasattr(reference_product, 'other_material') else False}")

                    if hasattr(reference_product, 'x_is_bag'):
                        copy_vals['x_is_bag'] = reference_product.x_is_bag
                        _logger.info(f"Copying x_is_bag: {reference_product.x_is_bag}")

                    if hasattr(reference_product, 'x_bag_weight'):
                        copy_vals['x_bag_weight'] = reference_product.x_bag_weight
                        _logger.info(f"Copying x_bag_weight: {reference_product.x_bag_weight}")

                    if hasattr(reference_product.product_tmpl_id, 'split_method_landed_cost'):
                        copy_vals['split_method_landed_cost'] = reference_product.product_tmpl_id.split_method_landed_cost
                        _logger.info(f"Copying split_method_landed_cost: {reference_product.product_tmpl_id.split_method_landed_cost}")

                    # Field from ai_bt_sales_extension
                    if hasattr(reference_product.product_tmpl_id, 'allow_negative_inventory'):
                        copy_vals['allow_negative_inventory'] = reference_product.product_tmpl_id.allow_negative_inventory
                        _logger.info(f"Copying allow_negative_inventory: {reference_product.product_tmpl_id.allow_negative_inventory}")

                    # Field for landed costs
                    if hasattr(reference_product.product_tmpl_id, 'landed_cost_ok'):
                        copy_vals['landed_cost_ok'] = reference_product.product_tmpl_id.landed_cost_ok
                        _logger.info(f"Copying landed_cost_ok: {reference_product.product_tmpl_id.landed_cost_ok}")

                    # Field from ai_bt_sales_extension
                    # if hasattr(reference_product, 'x_quintal_rate'):
                    #     copy_vals['x_quintal_rate'] = reference_product.x_quintal_rate
                    #     _logger.info(f"Copying x_quintal_rate: {reference_product.x_quintal_rate}")

                    # Make sure the avg_cost_per_weight is preserved if it exists on the product
                    # This is normally computed on stock.lot but might be relevant for the product
                    # if hasattr(reference_product, 'avg_cost_per_weight'):
                    #     copy_vals['avg_cost_per_weight'] = reference_product.avg_cost_per_weight
                    #     _logger.info(f"Copying avg_cost_per_weight from product: {reference_product.avg_cost_per_weight}")

                    # Stock move related fields that might be on the product
                    for field in ['sale_product', 'riclin_product', 'clean_product', 'waste_product']:
                        if hasattr(reference_product, field):
                            copy_vals[field] = getattr(reference_product, field)
                            _logger.info(f"Copying {field}: {getattr(reference_product, field)}")

                    # Copy any custom fields with x_ prefix that we might have missed
                    for field_name in dir(reference_product):
                        if field_name.startswith('x_') and field_name not in copy_vals and hasattr(reference_product, field_name):
                            try:
                                # Skip computed fields, methods, and private attributes
                                if callable(getattr(reference_product.__class__, field_name, None)) or field_name.startswith('_'):
                                    continue

                                # Get the field value
                                field_value = getattr(reference_product, field_name)

                                # Skip fields that are not simple types (like relations)
                                if isinstance(field_value, models.BaseModel):
                                    continue

                                # Add the field to copy_vals
                                copy_vals[field_name] = field_value
                                _logger.info(f"Copying custom field {field_name}: {field_value}")
                            except Exception as e:
                                _logger.warning(f"Could not copy custom field {field_name}: {str(e)}")

                    # Create the new product with all the copied values
                    new_product = reference_product.copy(copy_vals)
                    _logger.info(f"Successfully created new product (ID: {new_product.id})")

                    # Log the values of the new product for debugging
                    if hasattr(new_product, 'raw_material'):
                        _logger.info(f"New product raw_material value: {new_product.raw_material}")
                    if hasattr(new_product, 'other_material'):
                        _logger.info(f"New product other_material value: {new_product.other_material}")

                    # Double-check that raw_material and other_material were properly copied
                    # If not, update them directly
                    if hasattr(reference_product, 'raw_material') and hasattr(new_product, 'raw_material'):
                        if new_product.raw_material != reference_product.raw_material:
                            _logger.warning(f"raw_material not properly copied. Fixing: {new_product.raw_material} -> {reference_product.raw_material}")
                            new_product.write({'raw_material': reference_product.raw_material})

                    if hasattr(reference_product, 'other_material') and hasattr(new_product, 'other_material'):
                        if new_product.other_material != reference_product.other_material:
                            _logger.warning(f"other_material not properly copied. Fixing: {new_product.other_material} -> {reference_product.other_material}")
                            new_product.write({'other_material': reference_product.other_material})

                    # Also update the product template directly to ensure the fields are set there too
                    template_vals = {}
                    if hasattr(reference_product.product_tmpl_id, 'raw_material'):
                        template_vals['raw_material'] = reference_product.product_tmpl_id.raw_material
                    if hasattr(reference_product.product_tmpl_id, 'other_material'):
                        template_vals['other_material'] = reference_product.product_tmpl_id.other_material

                    if template_vals:
                        _logger.info(f"Updating product template with values: {template_vals}")
                        new_product.product_tmpl_id.write(template_vals)
                except Exception as e:
                    _logger.error(f"Error creating product: {str(e)}")
                    raise UserError(_("Failed to create product: %s") % str(e))

            # Check if this product already exists in the order lines
            existing_line = self.order_line.filtered(lambda l: l.product_id.id == new_product.id)

            if existing_line:
                _logger.info(f"Product already exists in order lines, not creating a new line")
                # Return a simple notification
                return {
                    'type': 'ir.actions.client',
                    'tag': 'display_notification',
                    'params': {
                        'title': _('Warning'),
                        'message': _('Product "%s" already exists in the order lines.') % new_product_name,
                        'sticky': False,
                        'type': 'warning',
                    }
                }

            # Create a new order line with the product
            _logger.info(f"Creating new order line with product (ID: {new_product.id})")

            # Get the purchase order line model
            PurchaseOrderLine = self.env['purchase.order.line']

            # Set default values based on product type
            product_weight = new_product.weight or 0.0

            # Check if the product has the x_is_bag field
            is_bag = False
            if hasattr(new_product, 'x_is_bag'):
                is_bag = new_product.x_is_bag
                _logger.info(f"Product {new_product.name} has x_is_bag = {is_bag}")
            else:
                _logger.info(f"Product {new_product.name} does not have x_is_bag field")

            # Calculate price_unit based on product type
            if new_product.type == 'consu' and not is_bag:
                # For consumable products (not bags):
                # Bharati Weight is the weight value entered by the user (e.g., 37KG)
                # x_product_rate should be the price for 20kg of the product
                # price_unit is calculated based on the product's weight and the 20kg rate

                # Calculate the 20kg rate based on the reference product's standard_price and weight
                if reference_product.weight and reference_product.weight > 0:
                    # standard_price is the price per weight unit, so we calculate the 20kg rate
                    # Formula: (standard_price / weight) * 20
                    x_product_rate = (reference_product.standard_price / reference_product.weight) * 20
                    _logger.info(f"Calculated 20kg rate from reference product: standard_price={reference_product.standard_price}, "
                                f"weight={reference_product.weight}, x_product_rate={x_product_rate}")
                else:
                    # Fallback to a default value if weight is zero
                    x_product_rate = 100.0
                    _logger.warning(f"Reference product has zero weight, using default 20kg rate: {x_product_rate}")

                # Calculate price_unit based on the formula: (x_product_rate / 20) * product_weight
                price_unit = (x_product_rate / 20) * product_weight

                _logger.info(f"Consumable product (not bag): x_product_rate={x_product_rate} (20kg rate), price_unit={price_unit} (per {product_weight}kg)")
            else:
                # For service products or bags, price_unit = x_product_rate
                x_product_rate = new_product.standard_price
                price_unit = x_product_rate
                _logger.info(f"Service or bag product: x_product_rate={x_product_rate}, price_unit={price_unit}")

            # Ensure price is greater than zero
            if price_unit <= 0:
                if new_product.type == 'consu' and not is_bag:
                    # For consumable products, calculate the 20kg rate from the reference product
                    if reference_product.weight and reference_product.weight > 0 and reference_product.standard_price > 0:
                        x_product_rate = (reference_product.standard_price / reference_product.weight) * 20
                        _logger.info(f"Recalculated 20kg rate from reference product: {x_product_rate}")
                    else:
                        # Fallback to a default value
                        x_product_rate = 100.0
                        _logger.warning(f"Using default 20kg rate for fallback: {x_product_rate}")

                    price_unit = (x_product_rate / 20) * product_weight
                    if price_unit <= 0:
                        price_unit = 1.0  # Fallback if weight is zero
                else:
                    # For service products or bags, set a default rate of 1.0
                    x_product_rate = 1.0
                    price_unit = 1.0

                _logger.warning(f"Product price was zero or negative, defaulting to price_unit={price_unit}, x_product_rate={x_product_rate}")

            # Create the new order line with all required fields
            line_vals = {
                'order_id': self.id,
                'product_id': new_product.id,
                'name': new_product.name,
                'product_qty': 1.0,
                'product_uom': new_product.uom_id.id,
                'price_unit': price_unit,
                'date_planned': fields.Datetime.now(),
                'x_product_rate': x_product_rate,  # Set product rate according to the formula
                'x_bank_payment': price_unit * 1.0,  # Default to bank payment for the full amount (price_unit * qty)
                'x_cash_payment': 0.0,         # Default cash payment to zero
                'x_product_loose_weight': 0.0  # Default loose weight to zero
            }

            _logger.info(f"Creating new order line with values: {line_vals}")
            new_line = PurchaseOrderLine.create(line_vals)

            _logger.info(f"Successfully created new order line (ID: {new_line.id})")

            # Clear the fields after successful creation
            self.x_reference_product_id = False
            self.x_bharati_weight = 0.0

            _logger.info(f"Product variant creation and line addition completed successfully: {new_product_name}")

            # Return a simple notification and reload the view
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Success'),
                    'message': _('Product "%s" has been created and added to the order lines.') % new_product_name,
                    'sticky': False,
                    'type': 'success',
                    'next': {
                        'type': 'ir.actions.act_window_close',
                    }
                }
            }

        except Exception as e:
            if not isinstance(e, UserError):
                _logger.exception("Unexpected error in product variant creation")
                raise UserError(_("An unexpected error occurred: %s") % str(e))
            raise
