<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <data>
        <!--views-->
        <record id="stock_report_wizard_view_form" model="ir.ui.view">
            <field name="name">stock.report.wizard.view.form</field>
            <field name="model">stock.report.wizard</field>
            <field name="arch" type="xml">
                <form>
                    <group string="Dates">
                        <group>
                            <field name="start_date"/>
                        </group>
                        <group>
                            <field name="end_date"/>
                        </group>
                    </group>
                    <group string="Location/Company">
                        <group>
                            <field name="stock_location_id"/>
                        </group>
                        <group>
                            <field name="company_id"/>
                        </group>
                    </group>
                    <footer>
                        <div style="display: flex; justify-content: center; align-items: center;">
                            <button name="pdf_action" string="PDF" type="object" class="oe_highlight"/>
                            <span style="vertical-align: middle; margin: 0 5px;">or</span>
                            <button name="btn_excel_action" string="Excel" type="object" class="oe_highlight"/>
                            <span style="margin: 0 5px;">or</span>
                            <button special="cancel" string="Cancel" type="object"/>
                        </div>
                    </footer>
                </form>
            </field>
        </record>
        <!--action-->
        <record id="stock_report_wizard_action" model="ir.actions.act_window">
            <field name="name">Stock Card Report</field>
            <field name="res_model">stock.report.wizard</field>
            <field name="view_mode">form</field>
            <field name="target">new</field>
        </record>
    </data>
</odoo>