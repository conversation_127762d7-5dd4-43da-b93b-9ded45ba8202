from odoo import api, fields, models
from datetime import datetime
import logging

_logger = logging.getLogger(__name__)

class SaleOrder(models.Model):
    _inherit = 'sale.order'
    
    def update_date_order_from_validity_date(self):
        """
        Update date_order field from validity_date for all sales orders.
        This method can be used in a scheduled action.
        """
        # Get all sales orders with validity_date set
        sales_orders = self.search([('validity_date', '!=', False)])
        
        count = 0
        for order in sales_orders:
            try:
                # Convert validity_date (date) to datetime with default time
                validity_datetime = datetime.combine(
                    order.validity_date, 
                    datetime.min.time()
                )
                
                # Update date_order
                order.write({
                    'date_order': validity_datetime
                })
                count += 1
                
            except Exception as e:
                _logger.error(f"Error updating date_order for sale order {order.name}: {str(e)}")
                
        _logger.info(f"Updated date_order for {count} sales orders")
        return True
