import logging
from datetime import datetime, timedelta
from odoo import api, SUPERUSER_ID

_logger = logging.getLogger(__name__)

def post_init_hook(cr, registry):
    """
    Post-install hook to create the cron job for syncing product material fields.
    """
    _logger.info("Creating cron job for syncing product material fields")

    env = api.Environment(cr, SUPERUSER_ID, {})

    # Check if the cron job already exists
    existing_cron = env['ir.cron'].search([
        ('name', '=', 'Sync Raw/Other Material Fields Only'),
        ('model_id.model', '=', 'product.template'),
    ], limit=1)

    if existing_cron:
        _logger.info("Cron job for syncing product material fields already exists")
        return

    # Get the model ID for product.template
    model_id = env['ir.model'].search([('model', '=', 'product.template')], limit=1).id

    # Create the cron job
    nextcall = datetime.now() + timedelta(days=1)
    nextcall = nextcall.replace(hour=0, minute=0, second=0)

    env['ir.cron'].create({
        'name': 'Sync Raw/Other Material Fields Only',
        'model_id': model_id,
        'state': 'code',
        'code': 'model.sync_material_fields_for_all_products()',
        'interval_number': 1,
        'interval_type': 'days',
        'numbercall': -1,
        'doall': False,
        'active': False,
        'nextcall': nextcall.strftime('%Y-%m-%d %H:%M:%S'),
    })

    _logger.info("Cron job for syncing product material fields created successfully")
