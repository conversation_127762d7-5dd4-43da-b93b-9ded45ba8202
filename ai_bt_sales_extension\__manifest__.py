{
    'name': 'AI BT Sales Extension',
    'version': '1.0',
    'category': 'Sales',
    'summary': 'Extension for Sales Module based on Purchase Module Adaptations',
    'description': """
        This module extends the sales module by adapting features and logic from the purchase module.
    """,
    'author': 'Arihant AI',
    'depends': ['sale',
                'purchase',
                'stock',
                'mrp',
                'sale_management',
                'product',
                'base_automation',
                'ai_bt_spices_module',],
    'data': [
        'security/security.xml',
        'security/ir.model.access.csv',
        'views/sales_views.xml',
        'views/sales_agent_commission_views.xml',
        'views/sale_order_views.xml',
        'views/sale_order_list_view.xml',
        'views/sale_quotation_list_view.xml',
        'views/sale_order_date_fields_view.xml',        
    ],
    'installable': True,
    'application': False,
    'auto_install': False,
    'assets': {
        'web.assets_backend': [
            # Add any CSS or JS assets here if needed
        ],
    },
}
