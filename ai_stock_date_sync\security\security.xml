<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data>
        <!-- Create a security group for users who can sync stock dates -->
        <record id="group_stock_date_sync" model="res.groups">
            <field name="name">Stock Date Synchronization</field>
            <field name="category_id" ref="base.module_category_inventory_inventory"/>
            <field name="implied_ids" eval="[(4, ref('stock.group_stock_manager'))]"/>
            <field name="users" eval="[(4, ref('base.user_admin'))]"/>
        </record>
    </data>
</odoo>
