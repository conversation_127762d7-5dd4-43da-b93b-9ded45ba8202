# -*- coding: utf-8 -*-
from odoo.tests import common, tagged


@tagged('post_install', '-at_install')
class TestDomainFilter(common.TransactionCase):
    """Test the domain filter functionality when scanning barcodes."""

    def setUp(self):
        super(TestDomainFilter, self).setUp()
        # Create test products
        self.product1 = self.env['product.product'].create({
            'name': 'Test Product 1',
            'type': 'product',
            'default_code': 'TP1',
        })
        self.product2 = self.env['product.product'].create({
            'name': 'Test Product 2',
            'type': 'product',
            'default_code': 'TP2',
        })

        # Create a test lot number that's associated with both products
        self.lot_name = 'TEST123'
        self.lot1 = self.env['stock.lot'].create({
            'name': self.lot_name,
            'product_id': self.product1.id,
            'company_id': self.env.company.id,
        })
        self.lot2 = self.env['stock.lot'].create({
            'name': self.lot_name,
            'product_id': self.product2.id,
            'company_id': self.env.company.id,
        })

        # Create a sale order
        self.partner = self.env['res.partner'].create({
            'name': 'Test Customer',
        })
        self.sale_order = self.env['sale.order'].create({
            'partner_id': self.partner.id,
        })

    def test_sale_order_line_domain_filter_no_barcode(self):
        """Test that domain filter shows all products when no barcode is scanned."""
        sale_line = self.env['sale.order.line'].create({
            'order_id': self.sale_order.id,
            'product_id': self.product1.id,
            'product_uom_qty': 1.0,
        })

        # Check domain filter when no barcode is set
        self.assertEqual(sale_line.product_domain_filter, "[(1, '=', 1)]",
                         "Should show all products when no barcode is scanned")

    def test_sale_order_line_domain_filter_with_lot(self):
        """Test that domain filter shows only relevant products when lot is scanned."""
        sale_line = self.env['sale.order.line'].create({
            'order_id': self.sale_order.id,
            'product_id': self.product1.id,
            'product_uom_qty': 1.0,
            'barcode_scan': self.lot_name,
        })

        # Check domain filter when lot is set
        expected_domain = str([('id', 'in', [self.product1.id, self.product2.id])])
        self.assertEqual(sale_line.product_domain_filter, expected_domain,
                         "Should show only products associated with the lot")

    def test_sale_order_line_domain_filter_invalid_lot(self):
        """Test that domain filter shows all products when invalid lot is scanned."""
        sale_line = self.env['sale.order.line'].create({
            'order_id': self.sale_order.id,
            'product_id': self.product1.id,
            'product_uom_qty': 1.0,
            'barcode_scan': 'INVALID_LOT',
        })

        # Check domain filter when invalid lot is set
        self.assertEqual(sale_line.product_domain_filter, "[(1, '=', 1)]",
                         "Should show all products when invalid lot is scanned")

    def test_stock_move_domain_filter_with_mrp_context(self):
        """Test that domain filter excludes parent product in MRP context."""
        # Create a manufacturing order
        mo = self.env['mrp.production'].create({
            'product_id': self.product1.id,
            'product_qty': 1.0,
            'product_uom_id': self.product1.uom_id.id,
        })

        # Create a stock move for raw materials
        stock_move = self.env['stock.move'].create({
            'name': 'Test Move',
            'product_id': self.product2.id,
            'product_uom': self.product2.uom_id.id,
            'product_uom_qty': 1.0,
            'location_id': self.env.ref('stock.stock_location_stock').id,
            'location_dest_id': self.env.ref('stock.stock_location_customers').id,
            'raw_material_production_id': mo.id,
        })

        # Check domain filter when no barcode is set (should exclude parent product)
        expected_domain = str([('id', '!=', self.product1.id)])
        self.assertEqual(stock_move.product_domain_filter, expected_domain,
                         "Should exclude parent product in MRP context")

        # Set barcode scan to lot that includes parent product
        stock_move.barcode_scan = self.lot_name

        # Check domain filter (should exclude parent product from lot products)
        expected_domain = str([('id', 'in', [self.product2.id])])
        self.assertEqual(stock_move.product_domain_filter, expected_domain,
                         "Should exclude parent product from lot products in MRP context")

    def test_onchange_barcode_scan_product_selection(self):
        """Test that onchange sets the correct product when barcode is scanned."""
        sale_line = self.env['sale.order.line'].create({
            'order_id': self.sale_order.id,
            'product_uom_qty': 1.0,
        })

        # Test with lot number that has multiple products
        sale_line.barcode_scan = self.lot_name
        sale_line._onchange_barcode_scan()

        # Should set the first product found
        self.assertIn(sale_line.product_id.id, [self.product1.id, self.product2.id],
                      "Should set one of the products associated with the lot")

        # Test clearing barcode
        sale_line.barcode_scan = False
        sale_line._onchange_barcode_scan()

        # Should clear product
        self.assertFalse(sale_line.product_id, "Should clear product when barcode is cleared")

    def test_product_selection_wizard_creation(self):
        """Test that wizard is created when multiple products are found for a lot"""
        sale_line = self.env['sale.order.line'].create({
            'order_id': self.sale_order.id,
            'product_uom_qty': 1.0,
        })

        # Test with lot number that has multiple products
        sale_line.barcode_scan = self.lot_name
        result = sale_line._onchange_barcode_scan()

        # Should return wizard action when multiple products found
        self.assertIsInstance(result, dict, "Should return action dictionary")
        self.assertEqual(result.get('res_model'), 'product.selection.wizard',
                         "Should open product selection wizard")
        self.assertEqual(result.get('target'), 'new', "Should open wizard in new window")

    def test_product_selection_wizard_functionality(self):
        """Test the product selection wizard functionality"""
        # Create wizard
        wizard = self.env['product.selection.wizard'].create({
            'lot_number': self.lot_name,
            'source_model': 'sale.order.line',
            'source_record_id': 1,  # Dummy ID for test
        })

        # Create wizard lines
        wizard.line_ids = [(0, 0, {
            'product_id': self.product1.id,
            'is_selected': True,
        }), (0, 0, {
            'product_id': self.product2.id,
            'is_selected': False,
        })]

        # Test that stock information is populated
        selected_line = wizard.line_ids.filtered('is_selected')
        self.assertTrue(selected_line, "Should have a selected line")
        self.assertEqual(selected_line.product_id, self.product1, "Should select correct product")
