# -*- coding: utf-8 -*-
################################################################################
#
#    Cybrosys Technologies Pvt. Ltd.
#
#    Copyright (C) 2024-TODAY Cybrosys Technologies(<https://www.cybrosys.com>).
#    Author:  <PERSON><PERSON> (<EMAIL>)
#
#    You can modify it under the terms of the GNU AFFERO
#    GENERAL PUBLIC LICENSE (AGPL v3), Version 3.
#
#    This program is distributed in the hope that it will be useful,
#    but WITHOUT ANY WARRANTY; without even the implied warranty of
#    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
#
#    You should have received a copy of the GNU AFFERO GENERAL PUBLIC LICENSE
#    (AGPL v3) along with this program.
#    If not, see <http://www.gnu.org/licenses/>.
#
################################################################################
from odoo import api, fields, models
import logging

_logger = logging.getLogger(__name__)


class StockMove(models.Model):
    """Inherited the model for add field for barcode."""
    _inherit = "stock.move"

    barcode_scan = fields.Char(string='Lot No',
                               help="Enter the lot number or scan product barcode. "
                                    "The system will find the product while preserving the lot number value.")
    filtered_product_ids = fields.Char(string="Filtered Product IDs", copy=False,
                                     help="Technical field to store product IDs filtered by lot number")

    def _get_product_domain(self):
        """Method to get the domain for product_id field based on barcode_scan.
        This can be called directly from the view."""
        self.ensure_one()
        if not self.barcode_scan:
            return []

        # Check if there are products associated with this lot number
        lots = self.env['stock.lot'].search([('name', '=', self.barcode_scan)])
        if lots:
            product_ids = lots.mapped('product_id.id')
            if product_ids:
                # Log for debugging
                _logger.info("_get_product_domain: Found products %s for lot %s", product_ids, self.barcode_scan)
                return [('id', 'in', product_ids)]

        # Log for debugging
        _logger.info("_get_product_domain: No products found for lot %s", self.barcode_scan)
        return []

    def show_lot_products(self):
        """Show all products associated with the lot number in a popup and allow selection."""
        self.ensure_one()

        # Add explicit logging
        import logging
        logger = logging.getLogger(__name__)
        logger.info("=== show_lot_products called for move %s with barcode_scan=%s ===", self.id, self.barcode_scan)

        if not self.barcode_scan:
            logger.warning("No barcode_scan value provided")
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'No Lot Number',
                    'message': 'Please enter a lot number first.',
                    'type': 'warning',
                }
            }

        # Get products associated with the lot number
        filtered_product_ids = self.get_products_by_lot()
        logger.info("Got filtered_product_ids: %s", filtered_product_ids)

        if not filtered_product_ids:
            logger.warning("No products found for lot number %s", self.barcode_scan)
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'No Products Found',
                    'message': f'No products found for lot number {self.barcode_scan}',
                    'type': 'warning',
                }
            }

        # If there's only one product, just show it
        if len(filtered_product_ids) == 1:
            # Return an action to show the product
            return {
                'name': f'Product for Lot {self.barcode_scan}',
                'type': 'ir.actions.act_window',
                'res_model': 'product.product',
                'view_mode': 'form',
                'res_id': filtered_product_ids[0],
                'target': 'new',
            }
        # If there are multiple products, pre-select the first one and open the selection wizard
        else:
            # Set the first product as default
            self.product_id = self.env['product.product'].browse(filtered_product_ids[0])

            # Open the product selection wizard
            wizard_action = {
                'name': 'Select Product',
                'type': 'ir.actions.act_window',
                'res_model': 'product.lot.selection.wizard',
                'view_mode': 'form',
                'view_id': self.env.ref('barcode_scanning_sale_purchase.view_product_lot_selection_wizard_form').id,
                'target': 'new',
                'context': {
                    'default_lot_number': self.barcode_scan,
                    'default_stock_move_id': self.id,
                    'default_available_product_ids': [(6, 0, filtered_product_ids)],
                    'default_selected_product_id': filtered_product_ids[0],
                }
            }

            # Enhanced logging for the wizard action
            logger.info("Opening product selection wizard with context: %s", wizard_action['context'])
            logger.info("Wizard view ID: %s", wizard_action['view_id'])
            logger.info("Full wizard action: %s", wizard_action)

            # Return the wizard action
            return wizard_action

    def get_products_by_lot(self):
        """Get products associated with the lot number in barcode_scan field."""
        self.ensure_one()
        if not self.barcode_scan:
            print("No barcode_scan value")
            return []

        # Check if there are products associated with this lot number
        lots = self.env['stock.lot'].search([('name', '=', self.barcode_scan)])
        print(f"Found {len(lots)} lots for barcode {self.barcode_scan}: {lots.mapped('name')}")

        if lots:
            product_ids = lots.mapped('product_id.id')
            print(f"Found {len(product_ids)} product IDs for lots: {product_ids}")

            # Get the parent product ID if in MRP context
            parent_product_id = False
            if self.raw_material_production_id and self.raw_material_production_id.product_id:
                parent_product_id = self.raw_material_production_id.product_id.id
            elif self.other_material_production_id and self.other_material_production_id.product_id:
                parent_product_id = self.other_material_production_id.product_id.id

            # Filter out the parent product if needed
            filtered_product_ids = product_ids.copy()
            if parent_product_id and parent_product_id in filtered_product_ids:
                filtered_product_ids.remove(parent_product_id)
                print(f"Removed parent product {parent_product_id} from filtered products")

            print(f"Returning {len(filtered_product_ids)} filtered product IDs: {filtered_product_ids}")
            return filtered_product_ids

        print("No lots found, returning empty list")
        return []







    @api.onchange('barcode_scan')
    def _onchange_barcode_scan(self):
        """Onchange function for searching product using their barcode or lot/serial number
        while preserving the entered lot number value"""
        # Add explicit logging at the start of the method
        import logging
        logger = logging.getLogger(__name__)
        logger.info("=== _onchange_barcode_scan called with barcode_scan=%s ===", self.barcode_scan)

        # Get the parent product ID from the context
        # In MRP context, we need to exclude the parent product from the domain
        parent_product_id = False

        # Check if we're in a Manufacturing Order context
        active_mo_id = self.env.context.get('active_mo_id', False)
        raw_material_production_id = self.raw_material_production_id.id or self.env.context.get('default_raw_material_production_id', False)
        other_material_production_id = self.other_material_production_id.id or self.env.context.get('default_other_material_production_id', False)

        # Determine the MO ID from any of the possible sources
        mo_id = active_mo_id or raw_material_production_id or other_material_production_id

        if mo_id:
            mo = self.env['mrp.production'].browse(mo_id)
            if mo and mo.product_id:
                parent_product_id = mo.product_id.id

        # First try to find product by barcode
        if self.barcode_scan:
            product = self.env['product.product'].search(
                [('barcode', '=', self.barcode_scan)], limit=1)

            if product:
                # Check if the product is not the parent product in MRP context
                if parent_product_id and product.id == parent_product_id:
                    # Show warning that the product is the same as the parent product
                    return {
                        'warning': {
                            'title': 'Invalid Product',
                            'message': 'You cannot select the same product as the manufacturing product.'
                        }
                    }
                else:
                    if product:
                        self.product_id = product
                        return

            # If no product found by barcode, try to find by lot/serial number
            # Get products associated with the lot number
            filtered_product_ids = self.get_products_by_lot()

            # Add debugging
            print(f"Found {len(filtered_product_ids)} products for lot {self.barcode_scan}: {filtered_product_ids}")

            # If products found for the lot
            if filtered_product_ids:
                # Store the filtered product IDs in the invisible field
                self.filtered_product_ids = str(filtered_product_ids)

                # Enhanced logging for debugging
                import logging
                _logger = logging.getLogger(__name__)
                _logger.info("Found products for lot %s: %s", self.barcode_scan, filtered_product_ids)

                # If there's only one product, select it directly
                if len(filtered_product_ids) == 1:
                    # Set the product
                    self.product_id = self.env['product.product'].browse(filtered_product_ids[0])

                    # If this is a move line that can have a lot assigned, assign it
                    if hasattr(self, 'move_line_ids') and self.move_line_ids:
                        lots = self.env['stock.lot'].search([('name', '=', self.barcode_scan)])
                        matching_lot = lots.filtered(lambda l: l.product_id.id == filtered_product_ids[0])
                        if matching_lot:
                            for move_line in self.move_line_ids:
                                if not move_line.lot_id and move_line.product_id.id == filtered_product_ids[0]:
                                    move_line.lot_id = matching_lot[0]
                                    break

                    # Return a warning to inform the user
                    return {
                        'warning': {
                            'title': 'Product Selected',
                            'message': f'Selected product {self.product_id.name} from lot {self.barcode_scan}'
                        }
                    }
                # If there are multiple products, pre-select the first one and open the selection wizard
                else:
                    # Set the first product as default
                    self.product_id = self.env['product.product'].browse(filtered_product_ids[0])

                    # Open the product selection wizard
                    wizard_action = {
                        'name': 'Select Product',
                        'type': 'ir.actions.act_window',
                        'res_model': 'product.lot.selection.wizard',
                        'view_mode': 'form',
                        'view_id': self.env.ref('barcode_scanning_sale_purchase.view_product_lot_selection_wizard_form').id,
                        'target': 'new',
                        'context': {
                            'default_lot_number': self.barcode_scan,
                            'default_stock_move_id': self.id,
                            'default_available_product_ids': [(6, 0, filtered_product_ids)],
                            'default_selected_product_id': filtered_product_ids[0],
                        }
                    }

                    # Enhanced debugging for the wizard action
                    import logging
                    logger = logging.getLogger(__name__)
                    logger.info("Opening product selection wizard with context: %s", wizard_action['context'])
                    logger.info("Wizard view ID: %s", wizard_action['view_id'])
                    logger.info("Full wizard action: %s", wizard_action)

                    # Return the wizard action
                    return wizard_action
            else:
                # No products found for this lot number
                return {
                    'warning': {
                        'title': 'No Valid Products',
                        'message': 'No valid products found for this lot number.'
                    }
                }

        # Reset filtered_product_ids if no barcode_scan or no products found
        self.filtered_product_ids = "[]"

        # Return a domain that shows all products except parent
        if parent_product_id:
            return {'domain': {'product_id': [('id', '!=', parent_product_id)]}}
        else:
            return {'domain': {'product_id': []}}
