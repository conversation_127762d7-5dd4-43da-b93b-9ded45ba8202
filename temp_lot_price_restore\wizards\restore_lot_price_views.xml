<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Form View -->
    <record id="view_restore_lot_price_wizard_form" model="ir.ui.view">
        <field name="name">restore.lot.price.wizard.form</field>
        <field name="model">restore.lot.price.wizard</field>
        <field name="arch" type="xml">
            <form string="Restore Lot Prices">
                <sheet>
                    <group>
                        <group>
                            <field name="date_from"/>
                            <field name="date_to"/>
                            <field name="update_method" invisible="1"/>
                            <field name="preview_only"/>
                        </group>
                        <group>
                            <field name="include_zero_cost_lots"/>
                            <field name="only_zero_cost_lots"/>
                            <field name="exclude_manual_modifications"/>
                            <field name="receipt_pickings_only"/>
                        </group>
                    </group>

                    <group string="Filters">
                        <group>
                            <field name="product_ids" widget="many2many_tags" options="{'no_create': True}"/>
                        </group>
                        <group>
                            <field name="lot_ids" widget="many2many_tags" options="{'no_create': True}"/>
                        </group>
                    </group>

                    <group string="Scheduled Execution">
                        <group>
                            <field name="auto_schedule"/>
                            <field name="schedule_interval" invisible="auto_schedule == False" required="auto_schedule == True"/>
                        </group>
                        <group>
                        </group>
                    </group>

                    <group string="Statistics" invisible="total_lots == 0">
                        <group>
                            <field name="total_lots"/>
                            <field name="lots_with_zero_cost"/>
                        </group>
                        <group>
                            <field name="lots_to_update"/>
                            <field name="lots_with_valuation"/>
                        </group>
                    </group>

                    <group invisible="log_details == False">
                        <field name="log_details" nolabel="1" readonly="1" widget="html"/>
                    </group>
                </sheet>
                <footer>
                    <button name="action_restore_lot_prices" string="Restore Lot Prices" type="object" class="btn-primary" data-hotkey="q"/>
                    <button string="Cancel" class="btn-secondary" special="cancel" data-hotkey="z"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Action -->
    <record id="action_restore_lot_price_wizard" model="ir.actions.act_window">
        <field name="name">Restore Lot Prices</field>
        <field name="res_model">restore.lot.price.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>

    <!-- Menu Item -->
    <menuitem id="menu_restore_lot_price_wizard"
              name="Restore Lot Prices"
              action="action_restore_lot_price_wizard"
              parent="stock.menu_stock_inventory_control"
              sequence="100"/>
</odoo>
