<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <!-- Scheduled action to synchronize effective dates -->
        <record id="ir_cron_sync_effective_date" model="ir.cron">
            <field name="name">Stock: Synchronize Effective Dates</field>
            <field name="model_id" ref="stock.model_stock_picking"/>
            <field name="state">code</field>
            <field name="code">model.cron_sync_effective_date(days_ago=7)</field>
            <field name="interval_number">1</field>
            <field name="interval_type">days</field>
            <field name="numbercall">-1</field>
            <field name="doall" eval="False"/>
            <field name="active" eval="True"/>
            <field name="user_id" ref="base.user_root"/>
        </record>
    </data>
</odoo>
