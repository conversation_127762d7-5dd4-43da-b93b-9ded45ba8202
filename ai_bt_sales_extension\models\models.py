from odoo import api, fields, models
from datetime import datetime
from odoo.exceptions import UserError, ValidationError
import logging

_logger = logging.getLogger(__name__)
from odoo.tools import float_round, float_compare

class SaleOrder(models.Model):
    _inherit = 'sale.order'

    date_order = fields.Datetime(
        default=lambda self: self._get_default_date_order(),
        required=True
    )

    def _check_line_unicity(self, product_id):
        """Override to allow multiple lines with the same product"""
        # Always return True to allow duplicate products (product_id is intentionally unused)
        return True

    def _update_existing_line(self, product_id, quantity, **kwargs):
        """Override to prevent updating existing lines - always create new ones"""
        # Always return False to prevent line updates and force creation of new lines
        # Parameters are intentionally unused as we always return False
        return False

    total_cash_payment = fields.Float(string="Total Cash Payment", compute="_compute_total_cash_payment", store=True,
                                      default=0.0)
    total_bank_payment = fields.Float(string="Total Bank Payment", compute="_compute_total_bank_payment", store=True,
                                      default=0.0)

    @api.depends('order_line.x_cash_payment', 'order_line.x_bank_payment', 'order_line.product_id.x_is_bag')
    def _compute_total_cash_payment(self):
        logger = logging.getLogger(__name__)
        for order in self:
            total_cash = 0
            total_cash_service = 0

            for line in order.order_line:
                if not line.product_id.x_is_bag:
                    if line.product_id.type == 'consu':
                        total_cash += line.x_cash_payment * line.product_uom_qty * line.product_id.weight
                    else:
                        total_cash_service += line.x_cash_payment
                else:
                    total_cash_service += line.x_cash_payment
            logger.debug(f"Calculating total cash payment for order {order.id}")
            logger.debug(f"Total cash before service: {total_cash}")
            logger.debug(f"Total cash service: {total_cash_service}")
            logger.debug(f"Total cash payment: {total_cash + total_cash_service}")
            order.total_cash_payment = total_cash + total_cash_service

    @api.depends('order_line.x_cash_payment', 'order_line.x_bank_payment', 'order_line.product_id.x_is_bag')
    def _compute_total_bank_payment(self):
        logger = logging.getLogger(__name__)
        for order in self:
            total_bank = 0
            total_bank_service = 0

            for line in order.order_line:
                if not line.product_id.x_is_bag:
                    if line.product_id.type == 'consu':
                        total_bank += line.x_bank_payment * line.product_uom_qty * line.product_id.weight
                    else:
                        total_bank_service += line.x_bank_payment
                else:
                    total_bank_service += line.x_bank_payment
            logger.debug(f"Calculating total bank payment for order {order.id}")
            logger.debug(f"Total bank before service: {total_bank}")
            logger.debug(f"Total bank service: {total_bank_service}")
            logger.debug(f"Total bank payment: {total_bank + total_bank_service}")
            order.total_bank_payment = total_bank + total_bank_service

    @api.model
    def _get_default_date_order(self):
        default_date_str = self.env['ir.config_parameter'].sudo().get_param('sale.default_date_order', default=False)
        if default_date_str:
            try:
                default_date = datetime.strptime(default_date_str, '%Y-%m-%d %H:%M:%S')
            except ValueError:
                raise UserError(
                    "The default date format in system parameters is incorrect. Please use 'YYYY-MM-DD HH:MM:SS'.")
            return default_date
        return fields.Datetime.now()

    @api.model_create_multi
    def create(self, vals_list):
        orders = super(SaleOrder, self).create(vals_list)
        for vals in vals_list:
            if 'date_order' in vals:
                date_order = vals['date_order']
                if isinstance(date_order, str):
                    try:
                        date_order = datetime.strptime(date_order, '%Y-%m-%d %H:%M:%S')
                    except ValueError:
                        raise UserError('The date_order provided is not in the correct format.')

                if isinstance(date_order, datetime):
                    self.env['ir.config_parameter'].sudo().set_param(
                        'sale.default_date_order',
                        date_order.strftime('%Y-%m-%d %H:%M:%S')
                    )



        return orders

    def _action_launch_stock_rule(self, previous_product_uom_qty=False):
        res = super(SaleOrder, self)._action_launch_stock_rule(previous_product_uom_qty=previous_product_uom_qty)
        for order in self:
            for line in order.order_line:
                if line.product_id.x_is_bag:
                    for move in line.move_ids:
                        move.product_uom_qty = line.x_bag_quantity
                        move.quantity_done = line.x_bag_quantity
        return res

    def _create_invoices(self, grouped=False, final=False, date=None):
        invoices = super(SaleOrder, self)._create_invoices(grouped=grouped, final=final, date=date)
        for invoice in invoices:
            invoice.total_pending_cash = self.total_cash_payment
            invoice.total_pending_bank = self.total_bank_payment
            invoice.total_cash_payment = self.total_cash_payment
            invoice.total_bank_payment = self.total_bank_payment
        return invoices

    # def action_register_payment(self):
    #     """
    #     Custom payment registration method that bypasses the default payment dialog
    #     and directly processes payments based on cash and bank payment amounts.
    #     """
    #     for record in self:
    #         # Calculate the total amount due
    #         cash_payment = record.total_cash_paid
    #         bank_payment = record.total_bank_paid
    #         total_amount_due = record.amount_total - (cash_payment + bank_payment)

    #         # Log payment details for debugging
    #         _logger.info(f"Registering payments for record {record.id}: Cash - {cash_payment}, Bank - {bank_payment}, Total Amount Due - {total_amount_due}")

    #         # Create payment records directly
    #         if cash_payment > 0:
    #             cash_payment_record = self.env['account.payment'].create({
    #                 'amount': cash_payment,
    #                 'payment_type': 'inbound',  
    #                 'partner_id': record.partner_id.id,
    #                 'partner_type': 'customer',
    #                 'journal_id': self.env['account.journal'].search([('type', '=', 'cash')], limit=1).id,
    #                 'payment_method_line_id': self.env['account.payment.method.line'].search([('payment_method_id.code', '=', 'manual')], limit=1).id,
    #             })
    #             # Reconcile the payment with the invoice
    #             cash_payment_record.action_post()

    #         if bank_payment > 0:
    #             bank_payment_record = self.env['account.payment'].create({
    #                 'amount': bank_payment,
    #                 'payment_type': 'inbound',  
    #                 'partner_id': record.partner_id.id,
    #                 'partner_type': 'customer',
    #                 'journal_id': self.env['account.journal'].search([('type', '=', 'bank')], limit=1).id,
    #                 'payment_method_line_id': self.env['account.payment.method.line'].search([('payment_method_id.code', '=', 'manual')], limit=1).id,
    #             })
    #             # Reconcile the payment with the invoice
    #             bank_payment_record.action_post()

    #         # Mark the record as paid
    #         record.action_done()

    #     # Return an action to close the current window and prevent default payment dialog
    #     return {
    #         'type': 'ir.actions.act_window_close'
    #     }

    def action_register_payment(self):
        for record in self:
            # Calculate the total amount due
            cash_payment = record.total_cash_payment
            bank_payment = record.total_bank_payment
            # total_amount_due = record.amount_total - (cash_payment + bank_payment)  # Calculated but not used

            # Create payment records
            if cash_payment > 0:
                cash_journal = self.env['account.journal'].search([('type', '=', 'cash')], limit=1)
                if not cash_journal:
                    raise UserError("No cash journal found.")
                cash_payment_record = self.env['account.payment'].create({
                    'amount': cash_payment,
                    'payment_type': 'inbound',
                    'partner_id': record.partner_id.id,
                    'partner_type': 'customer',
                    'journal_id': cash_journal.id,
                    'payment_method_line_id': cash_journal.inbound_payment_method_line_ids[:1].id,
                })
                cash_payment_record.action_post()

            if bank_payment > 0:
                bank_journal = self.env['account.journal'].search([('type', '=', 'bank')], limit=1)
                if not bank_journal:
                    raise UserError("No bank journal found.")
                bank_payment_record = self.env['account.payment'].create({
                    'amount': bank_payment,
                    'payment_type': 'inbound',
                    'partner_id': record.partner_id.id,
                    'partner_type': 'customer',
                    'journal_id': bank_journal.id,
                    'payment_method_line_id': bank_journal.inbound_payment_method_line_ids[:1].id,
                })
                bank_payment_record.action_post()

            # Reconcile payments with the invoices
            for invoice in record.invoice_ids.filtered(
                    lambda inv: inv.state == 'posted' and inv.payment_state != 'paid'):
                invoice.js_assign_outstanding_credit(cash_payment_record.id)
                invoice.js_assign_outstanding_credit(bank_payment_record.id)

            # Mark the record as paid
            record.action_done()
        return {
            'type': 'ir.actions.act_window_close'
        }


class SaleOrderLine(models.Model):
    _inherit = 'sale.order.line'

    x_bag = fields.Many2one(
        'product.product',
        string='Bag',
        domain=[('x_is_bag', '=', True)],
        help='Select a product that is marked as a bag.'
    )
    x_bag_quantity = fields.Integer(
        string='Bag Quantity',
        store=True,
        help='Quantity of the selected bag product based on the main product quantity.'
    )

    x_product_rate = fields.Float(string='Product Rate', store=True)
    x_quintal_rate = fields.Float(string='Quintal Rate', compute='_compute_quintal_rate', store=True)
    x_cash_payment = fields.Float(string="Cash Payment", default=0.0)
    x_bank_payment = fields.Float(string="Bank Payment", default=0.0)
    x_product_loose_weight = fields.Float(string='Loose Weight', store=True)
    x_product_total_weight = fields.Float(string='Total Weight', compute='_compute_total_weight', store=True)

    line_sequence = fields.Integer(
        string='Sequence',
        default=10,
        help="Unique sequence number for each line of the same product"
    )

    @api.depends('x_product_rate')
    def _compute_quintal_rate(self):
        for line in self:
            line.x_quintal_rate = line.x_product_rate * 100  # 1 quintal = 100 kg

    @api.onchange('x_product_rate')
    def _onchange_product_rate(self):
        if self.x_product_rate:
            self._compute_quintal_rate()

    @api.depends('product_uom_qty','x_product_rate', 'product_id', 'product_id.weight')
    def _compute_price_unit(self):
        for line in self:
            # Default to 0
            line.price_unit = 0.0

            # Only proceed if we have both product and rate
            if line.x_product_rate and line.product_id:
                if line.product_id.type == 'consu' and not line.product_id.x_is_bag:
                    product_weight = line.product_id.weight or 0.0
                    if product_weight > 0:
                        line.price_unit = line.x_product_rate * product_weight
                        _logger.info('Calculating price: rate=%s, weight=%s -> price=%s',
                                     line.x_product_rate, product_weight, line.price_unit)
                else:
                    line.price_unit = line.x_product_rate  # For bags or services

    @api.model_create_multi
    def create(self, vals_list):
        # Handle multiple lines of the same product with different rates
        for vals in vals_list:
            if vals.get('order_id') and vals.get('product_id'):
                existing_lines = self.search([
                    ('order_id', '=', vals['order_id']),
                    ('product_id', '=', vals['product_id'])
                ])
                if existing_lines:
                    max_sequence = max(existing_lines.mapped('line_sequence'))
                    vals['line_sequence'] = max_sequence + 10

        orders = super(SaleOrderLine, self).create(vals_list)
        for vals in vals_list:
            if 'x_bag' in vals or 'x_bag_quantity' in vals:
                if self.product_id.x_is_bag:
                    if self.x_bag or self.x_bag_quantity:
                        raise UserError('You can not add Bags on Products of Type Bag.')
                if self.product_id.type == 'service':
                    raise UserError('You can not add Bags on Products of Type Service.')
                _logger.info("_recompute_bag_qty create def")
                self._recompute_bag_qty(orders.order_id)
        return orders

    def write(self, vals):
        res = super(SaleOrderLine, self).write(vals)
        if 'x_bag' in vals or 'x_bag_quantity' in vals:
            if self.product_id.x_is_bag:
                if self.x_bag or self.x_bag_quantity:
                    raise UserError('You can not add Bags on Products of Type Bag.')
            if self.product_id.type == 'service':
                raise UserError('You can not add Bags on Products of Type Service.')
            self._recompute_bag_qty(self.order_id)

        return res
    
    def unlink(self):
        """
        Ensure the bag quantity is updated after a product line deletion.
        """
        for line in self:
            _logger.info('Prinitng Names in unlink event')
            _logger.info(str(line.product_id.name))
            if line.x_bag:
                self._recompute_bag_qty(line.order_id, line.id)
        return super(SaleOrderLine, self).unlink()

    @api.constrains('product_id', 'product_uom_qty', 'x_product_rate')
    def _check_line_validity(self):
        """Ensure valid quantity and rate for each line"""
        for line in self:
            if line.product_uom_qty <= 0:
                raise ValidationError("Quantity must be greater than zero.")
            if line.x_product_rate and line.x_product_rate <= 0:
                raise ValidationError("Product rate must be greater than zero.")

    def _can_be_merged_with(self, other_line):
        """Override to prevent line merging - each line should remain separate"""
        # Always return False to prevent any line merging (other_line is intentionally unused)
        return False

    def _recompute_bag_qty(self, order, unlink_line_id=0):
        """
        Recomputes the quantity for the bag type purchase order line.
        If the bag quantity becomes zero, the corresponding purchase order line is removed.
        """
        # Find all lines that share the same bag type
        bag_product_ids = []
        _logger.info("_recompute_bag_qty0 create def")
        _logger.info(order)
        for l in order.order_line:
            # _logger.info("_recompute_bag_qty1 create def")
            if l.x_bag:
                _logger.info("_recompute_bag_qty2 create def")
                if l.x_bag.id not in bag_product_ids:
                    _logger.info("_recompute_bag_qty3 create def")
                    bag_product_ids.append(l.x_bag.id)
        remaining_lines = self.env['sale.order.line'].search(
            [['order_id', '=', order.id], ['product_id.x_is_bag', '=', True],
             ['product_id', 'not in', bag_product_ids]])
        remaining_lines.sudo().unlink()
        for bag_product_id in bag_product_ids:
            total_bag_qty = 0
            main_product_lines = self.env['sale.order.line'].search([
                ('order_id', '=', order.id),
                ('x_bag', '=', bag_product_id),
                ('id', 'not in', [unlink_line_id])
            ])
            bag_product = self.env['product.product'].search([
                ('id', '=', bag_product_id)
            ])

            for line in main_product_lines:
                total_bag_qty += line.x_bag_quantity

            # Find the corresponding bag line
            bag_line = self.env['sale.order.line'].search([
                ('order_id', '=', order.id),
                ('product_id', '=', bag_product_id)
            ], limit=1)

            if total_bag_qty > 0:
                _logger.info("total_bag_qty create def")
                if bag_line:
                    # Update the existing bag line with the new quantity
                    bag_line.write({
                        'product_uom_qty': total_bag_qty,
                        'x_bank_payment': bag_product.lst_price * total_bag_qty
                    })
                else:
                    # Create a new bag line if it doesn't exist
                    new_line = self.env['sale.order.line'].create({
                        'order_id': order.id,
                        'product_id': bag_product_id,
                        'product_uom_qty': total_bag_qty,
                        'price_unit': bag_product.lst_price,  # Adjust price if needed
                        'x_product_rate': bag_product.lst_price,
                        # 'x_bank_payment': bag_product.lst_price * total_bag_qty,
                    })
                    if total_bag_qty == 0:
                        new_line.write({
                            'x_bank_payment': bag_product.lst_price
                        })
                    else:
                        new_line.write({
                            'x_bank_payment': bag_product.lst_price * total_bag_qty
                        })

            elif bag_line:
                # Remove the bag line if the quantity is zero after deletion
                bag_line.unlink()

    @api.onchange('product_id')
    def _onchange_product_id(self):
        """Override standard onchange to prevent automatic line merging"""
        # Call the parent method but prevent line merging behavior
        super(SaleOrderLine, self)._onchange_product_id()

        # Allow multiple lines with the same product by not returning any warning
        # This prevents the standard Odoo behavior of merging lines
        return {}

    @api.onchange('x_product_rate')
    def _onchange_product_details(self):
        _logger.info("Onchange product details triggered.")
        self.price_subtotal = self.product_uom_qty * self.price_unit
        _logger.info(f"Price Subtotal: {self.price_subtotal}")

        if self.x_product_rate:
            # Calculate the 1kg rate
            one_kg_rate = self.x_product_rate

            # Total payment is the sum of cash and bank
            total_payment = self.x_cash_payment + self.x_bank_payment
            _logger.info('_onchange_product_rate Q total_payment : ' + str(total_payment))
            # If total_payment is zero, set bank payment to one_kg_rate

            if total_payment == 0:
                self.x_bank_payment = one_kg_rate
                self.x_cash_payment = 0
            else:
                # Maintain the ratio
                cash_ratio = self.x_cash_payment / total_payment
                bank_ratio = self.x_bank_payment / total_payment

                # Calculate new values based on the new one_kg_rate
                self.x_cash_payment = one_kg_rate * cash_ratio
                self.x_bank_payment = one_kg_rate * bank_ratio

            self._check_payments_within_limit(one_kg_rate)
            self._compute_price_unit()

    @api.onchange('x_bank_payment')
    def _onchange_bank_payment(self):
        if self.env.context.get('skip_onchange'):
            return
        total_payment = 0

        if self.product_id.type != 'service':
            total_payment = self.x_product_rate
        else:
            # total_payment = self.x_product_rate * self.product_qty
            if self.product_uom_qty != 0:
                total_payment = self.x_product_rate * self.product_uom_qty
            else:
                total_payment = self.x_product_rate

        # Ensure x_bank_payment doesn't exceed total_payment
        if self.x_bank_payment > total_payment:
            self.x_bank_payment = total_payment

        # Update cash payment
        self.with_context(skip_update_fee_lines=True).x_cash_payment = total_payment - self.x_bank_payment

        _logger.info('onchange cash_payment : ' + str(self.x_cash_payment))

        # Validate payments
        self._check_payments_within_limit(total_payment)


    @api.onchange('x_cash_payment')
    def _onchange_cash_payment(self):
        if self.env.context.get('skip_onchange'):
            return
        total_payment = 0

        if self.product_id.type != 'service':
            total_payment = self.x_product_rate
        else:
            if self.product_uom_qty != 0:
                total_payment = self.x_product_rate * self.product_uom_qty
            else:
                total_payment = self.x_product_rate

        # Ensure x_cash_payment doesn't exceed total_payment
        if self.x_cash_payment > total_payment:
            self.x_cash_payment = total_payment

        # Update bank payment
        self.with_context(skip_update_fee_lines=True).x_bank_payment = total_payment - self.x_cash_payment

        _logger.info('onchange x_bank_payment : ' + str(self.x_bank_payment))

        # Validate payments
        self._check_payments_within_limit(total_payment)

    def _check_payments_within_limit(self, total_payment):
        if self.x_cash_payment > total_payment:
            raise UserError(f"Cash Payment cannot exceed the total payment of {total_payment:.2f}.")
        if self.x_bank_payment > total_payment:
            raise UserError(f"Bank Payment cannot exceed the total payment of {total_payment:.2f}.")

    @api.depends('product_uom_qty', 'product_id', 'x_product_loose_weight')
    def _compute_total_weight(self):
        for line in self:
            if line.product_id and line.product_id.type == 'consu' and not line.product_id.x_is_bag:
                line.x_product_total_weight = line.product_uom_qty * line.x_product_loose_weight
            else:
                line.x_product_total_weight = 0.0

class ProductTemplate(models.Model):
    _inherit = 'product.template'

    allow_negative_inventory = fields.Boolean(string='Allow Negative Inventory', default=False)

# class StockMove(models.Model):
#     _inherit = 'stock.move'

#     def _action_done(self, cancel_backorder=False):
#         for move in self:
#             if move.product_id.product_tmpl_id.allow_negative_inventory:
#                 move.with_context(force_period_date=move.date)._action_done_negative_inventory()
#             else:
#                 super(StockMove, move)._action_done(cancel_backorder=cancel_backorder)

#     def _action_done_negative_inventory(self):
#     # Custom logic to allow negative inventory
#         for move in self:
#             move.quantity = move.product_uom_qty
#             move.state = 'done'
#             _logger.info("Location ID: %s, Type: %s", move.location_id, type(move.location_id))
#             _logger.info("Before _get_reserve_quantity: Location ID: %s, Type: %s", move.location_id, type(move.location_id))
#             move._update_reserved_quantity(move.product_uom_qty, move.product_uom_qty, move.location_id)
#             _logger.info("Inside _update_reserved_quantity: Location ID: %s, Type: %s", move.location_id, type(move.location_id))
#             move._create_account_move_line(move.product_uom_qty)
