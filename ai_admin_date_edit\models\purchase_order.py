from odoo import models, fields, api, _
from odoo.exceptions import UserError
import logging
from datetime import datetime

_logger = logging.getLogger(__name__)

class PurchaseOrder(models.Model):
    _inherit = 'purchase.order'

    can_edit_dates = fields.Boolean(
        compute='_compute_can_edit_dates',
        string='Can Edit Dates',
        help='Technical field to determine if the user can edit dates on confirmed purchase orders'
    )

    @api.depends('state')
    def _compute_can_edit_dates(self):
        """Determine if the user can edit dates on this purchase order."""
        is_admin_date_editor = self.env.user.has_group('ai_admin_date_edit.group_admin_date_edit')
        for order in self:
            # Allow editing dates if the user is in the admin date edit group and the order is confirmed
            order.can_edit_dates = is_admin_date_editor and order.state in ['purchase', 'done']

    def action_edit_dates(self):
        """Open a wizard to edit dates on the purchase order."""
        self.ensure_one()
        if not self.can_edit_dates:
            raise UserError(_("You don't have permission to edit dates on this purchase order."))

        return {
            'name': _('Edit Dates'),
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'res_model': 'purchase.order',
            'res_id': self.id,
            'target': 'new',
            'context': {
                'edit_dates': True,
            },
        }

    def write(self, vals):
        """Override write to allow changing dates on confirmed purchase orders."""
        # Check if we're editing dates and if the user has permission
        is_admin_date_editor = self.env.user.has_group('ai_admin_date_edit.group_admin_date_edit')
        is_editing_dates = any(field in vals for field in ['date_order', 'date_planned', 'date_approve'])

        # Store original values for later processing
        orders_with_date_changes = {}
        if is_editing_dates:
            for order in self:
                if order.state in ['purchase', 'done']:
                    changes = {}
                    if 'date_order' in vals and order.date_order != vals['date_order']:
                        changes['date_order'] = {'old': order.date_order, 'new': vals['date_order']}
                    if 'date_planned' in vals and order.date_planned != vals['date_planned']:
                        changes['date_planned'] = {'old': order.date_planned, 'new': vals['date_planned']}
                    if 'date_approve' in vals and order.date_approve != vals['date_approve']:
                        changes['date_approve'] = {'old': order.date_approve, 'new': vals['date_approve']}

                    if changes:
                        orders_with_date_changes[order.id] = changes

        # If editing dates on confirmed orders, ensure the user has permission
        if is_editing_dates and any(order.state in ['purchase', 'done'] for order in self):
            if not is_admin_date_editor:
                raise UserError(_("You don't have permission to edit dates on confirmed purchase orders."))

            # Log the date changes for audit purposes
            for order in self:
                if order.state in ['purchase', 'done']:
                    changes = []
                    if 'date_order' in vals and order.date_order != vals['date_order']:
                        changes.append(f"date_order: {order.date_order} -> {vals['date_order']}")
                    if 'date_planned' in vals and order.date_planned != vals['date_planned']:
                        changes.append(f"date_planned: {order.date_planned} -> {vals['date_planned']}")
                    if 'date_approve' in vals and order.date_approve != vals['date_approve']:
                        changes.append(f"date_approve: {order.date_approve} -> {vals['date_approve']}")

                    if changes:
                        _logger.info(f"Admin {self.env.user.name} changed dates on purchase order {order.name}: {', '.join(changes)}")

                        # Update related pickings if date_planned is changed
                        if 'date_planned' in vals and order.picking_ids:
                            for picking in order.picking_ids.filtered(lambda p: p.state not in ['done', 'cancel']):
                                picking.scheduled_date = vals['date_planned']
                                _logger.info(f"Updated scheduled_date on picking {picking.name} to {vals['date_planned']}")

        # Proceed with the write operation
        result = super(PurchaseOrder, self).write(vals)

        # Update related records if dates were changed
        if orders_with_date_changes and is_admin_date_editor:
            self._update_related_records_after_date_change(orders_with_date_changes)

        return result

    def _update_related_records_after_date_change(self, orders_with_date_changes):
        """Update related records when dates are changed on a confirmed purchase order."""
        for order_id, changes in orders_with_date_changes.items():
            order = self.browse(order_id)

            # Log the changes
            change_descriptions = []
            for field, values in changes.items():
                change_descriptions.append(f"{field}: {values['old']} -> {values['new']}")

            _logger.info(f"Updating related records for purchase order {order.name} after date changes: {', '.join(change_descriptions)}")

            # 1. Update invoice dates if date_order or date_approve was changed
            if 'date_order' in changes or 'date_approve' in changes:
                new_date = changes.get('date_approve', {}).get('new') or changes.get('date_order', {}).get('new')
                if new_date and order.invoice_ids:
                    for invoice in order.invoice_ids:
                        if invoice.state not in ['posted', 'cancel']:
                            invoice.write({
                                'invoice_date': new_date.date() if hasattr(new_date, 'date') else new_date,
                                'date': new_date.date() if hasattr(new_date, 'date') else new_date,
                            })
                            _logger.info(f"Updated invoice_date and date on invoice {invoice.name} to {new_date}")

            # 2. Update accounting entries related to this purchase order
            if 'date_approve' in changes or 'date_order' in changes:
                new_date = changes.get('date_approve', {}).get('new') or changes.get('date_order', {}).get('new')
                # Find all account moves related to this purchase order
                account_moves = self.env['account.move'].search([
                    '|',
                    ('invoice_origin', '=', order.name),
                    ('ref', '=', order.name)
                ])

                if account_moves:
                    for move in account_moves:
                        if move.state != 'posted':
                            move.write({'date': new_date})
                            _logger.info(f"Updated date on account move {move.name} to {new_date}")
                        else:
                            _logger.warning(f"Account move {move.name} is already posted and cannot be updated automatically")

            # 3. Update stock picking dates if date_planned was changed
            if 'date_planned' in changes and order.picking_ids:
                new_date = changes['date_planned']['new']
                for picking in order.picking_ids:
                    if picking.state not in ['done', 'cancel']:
                        picking.write({'scheduled_date': new_date})
                        _logger.info(f"Updated scheduled_date on picking {picking.name} to {new_date}")

            # 4. Update payment terms calculation if date_order was changed
            if 'date_order' in changes and order.payment_term_id:
                new_date = changes['date_order']['new']
                # Recalculate payment due dates
                payment_date = order.payment_term_id.compute(order.amount_total, new_date)[0][0]
                if payment_date:
                    _logger.info(f"Payment due date recalculated to {payment_date} based on new order date")

            # 5. Update stock moves directly linked to this purchase order
            if 'date_planned' in changes:
                new_date = changes['date_planned']['new']
                # Find all stock moves related to this purchase order
                stock_moves = self.env['stock.move'].search([
                    ('purchase_line_id', 'in', order.order_line.ids),
                    ('state', 'not in', ['done', 'cancel'])
                ])

                if stock_moves:
                    stock_moves.write({'date': new_date})
                    _logger.info(f"Updated date on {len(stock_moves)} stock moves to {new_date}")

            # 6. Update purchase order lines dates
            if 'date_planned' in changes:
                new_date = changes['date_planned']['new']
                order.order_line.filtered(lambda l: l.state not in ['cancel']).write({'date_planned': new_date})
                _logger.info(f"Updated date_planned on {len(order.order_line)} purchase order lines to {new_date}")

            # Add a note to the order for audit purposes
            order.message_post(
                body=_("Dates changed by %s. Related records have been updated accordingly: %s") %
                     (self.env.user.name, ', '.join(change_descriptions)),
                subtype_id=self.env.ref('mail.mt_note').id
            )

class PurchaseOrderLine(models.Model):
    _inherit = 'purchase.order.line'

    def write(self, vals):
        """Override write to allow changing dates on confirmed purchase order lines."""
        # Check if we're editing dates and if the user has permission
        is_admin_date_editor = self.env.user.has_group('ai_admin_date_edit.group_admin_date_edit')
        is_editing_dates = 'date_planned' in vals

        # If editing dates on confirmed order lines, ensure the user has permission
        if is_editing_dates and any(line.state in ['purchase', 'done'] for line in self):
            if not is_admin_date_editor:
                raise UserError(_("You don't have permission to edit dates on confirmed purchase order lines."))

            # Log the date changes for audit purposes
            for line in self:
                if line.state in ['purchase', 'done'] and line.date_planned != vals['date_planned']:
                    _logger.info(f"Admin {self.env.user.name} changed date_planned on purchase order line {line.id} "
                                f"(order {line.order_id.name}): {line.date_planned} -> {vals['date_planned']}")

                    # Update related move lines if date_planned is changed
                    if line.move_ids:
                        for move in line.move_ids.filtered(lambda m: m.state not in ['done', 'cancel']):
                            move.date = vals['date_planned']
                            _logger.info(f"Updated date on stock move {move.id} to {vals['date_planned']}")

        # Proceed with the write operation
        return super(PurchaseOrderLine, self).write(vals)
