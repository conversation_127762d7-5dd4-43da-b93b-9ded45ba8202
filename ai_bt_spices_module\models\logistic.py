from odoo import models, fields, api
from odoo.exceptions import UserError

class LogisticModule(models.Model):
    _name = 'logistic.module'
    _description = 'Logistic Module'

    name = fields.Char(string='Name', required=True, copy=False, readonly=True, default='New')
    date = fields.Date(string='Date', default=fields.Date.context_today, required=True)
    state = fields.Selection([
        ('draft', 'Draft'),
        ('validated', 'Validated'),
        ('done', 'Done')
    ], string='Status', default='draft', readonly=True)
    picking_ids = fields.Many2many('stock.picking', string='Stock Pickings', domain="[('state', 'not in', ['done', 'cancel'])]")
    picking_type = fields.Selection([
        ('incoming', 'Receipts'),
        ('outgoing', 'Delivery Orders'),
        ('internal', 'Internal Transfers')
    ], string='Operation Type', required=True)
    move_ids = fields.Many2many('stock.move', string='Stock Moves', compute='_compute_move_ids', store=True)

    @api.depends('picking_ids')
    def _compute_move_ids(self):
        for record in self:
            record.move_ids = record.picking_ids.mapped('move_ids_without_package')

    @api.model
    def create(self, vals):
        if vals.get('name', 'New') == 'New':
            for move in self.move_ids:
                if move.quantity_done > 0:
                    if move.product_id.tracking != 'none' and not move.lot_ids:
                        raise UserError(f"Please specify a lot/serial number for product {move.product_id.name}")
                if line.quantity_done > 0:
                    move = line.move_id
                    move.quantity_done = line.quantity_done
                    if line.lot_id:
                        move.lot_ids = [(4, line.lot_id.id)]
            for move in self.move_ids:
                move.quantity_done = move.product_uom_qty
                if move.product_id.tracking != 'none' and not move.lot_ids:
                    raise UserError(f"Please specify a lot/serial number for product {move.product_id.name}")
            for line in self.logistic_line_ids:
                move = line.move_id
                move.quantity_done = line.product_uom_qty
                if line.lot_id:
                    move.lot_ids = [(4, line.lot_id.id)]
            self.picking_ids.with_context(skip_backorder=True, skip_immediate=True).button_validate()
            self.move_ids = self.picking_ids.mapped('move_ids_without_package')

    @api.onchange('move_ids.quantity_done')
    def _onchange_move_quantity_done(self):
        for move in self.move_ids:
            if move.quantity_done > move.product_uom_qty:
                raise UserError(f"Quantity done cannot exceed the demand quantity for product {move.product_id.name}")

    def write(self, vals):
        res = super(LogisticModule, self).write(vals)
        if 'move_ids' in vals:
            for move in self.move_ids:
                if move.quantity_done > move.product_uom_qty:
                    raise UserError(f"Quantity done cannot exceed the demand quantity for product {move.product_id.name}")
        return res
    # product_id = fields.Many2one('product.product', string='Product', required=True)
    # product_uom_qty = fields.Float(string='Demand', digits='Product Unit of Measure', required=True)
    # quantity_done = fields.Float(string='Done', digits='Product Unit of Measure')
    # product_uom = fields.Many2one('uom.uom', string='Unit of Measure', required=True)
    # lot_id = fields.Many2one('stock.lot', string='Lot/Serial Number')

    @api.onchange('quantity_done')
    def _onchange_quantity_done(self):
        if self.quantity_done > self.product_uom_qty:
            raise UserError('Quantity done cannot exceed the demand quantity.')