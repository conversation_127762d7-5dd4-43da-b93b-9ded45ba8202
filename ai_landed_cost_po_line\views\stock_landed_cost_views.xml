<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_stock_landed_cost_form_inherited" model="ir.ui.view">
        <field name="name">stock.landed.cost.form.inherited</field>
        <field name="model">stock.landed.cost</field>
        <field name="inherit_id" ref="stock_landed_costs.view_stock_landed_cost_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='cost_lines']/list//field[@name='price_unit']" position="attributes">
                <attribute name="digits">[16, 9]</attribute>
            </xpath>
            
            <xpath expr="//field[@name='valuation_adjustment_lines']/list//field[@name='additional_landed_cost']" position="attributes">
                <attribute name="digits">[16, 9]</attribute>
            </xpath>
            <xpath expr="//field[@name='valuation_adjustment_lines']/list//field[@name='final_cost']" position="attributes">
                <attribute name="digits">[16, 9]</attribute>
            </xpath>
        </field>
    </record>

    <!-- <record id="view_stock_landed_cost_list_inherited" model="ir.ui.view">
        <field name="name">stock.landed.cost.list.inherited</field>
        <field name="model">stock.landed.cost</field>
        <field name="inherit_id" ref="stock_landed_costs.view_stock_landed_cost_tree"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='amount_total']" position="attributes">
                <attribute name="digits">[16, 9]</attribute>
            </xpath>
        </field>
    </record> -->
</odoo>
