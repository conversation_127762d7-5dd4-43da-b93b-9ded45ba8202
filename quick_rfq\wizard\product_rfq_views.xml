<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <!-- Product rfq wizard form view -->
    <record id="product_rfq_view_form" model="ir.ui.view">
        <field name="name">product.rfq.view.form</field>
        <field name="model">product.rfq</field>
        <field name="arch" type="xml">
            <form>
                <sheet>
                    <group>
                        <group>
                            <field name="partner_id"/>
                            <field name="date_order"/>
                        </group>
                        <group>
                            <field name="user_id"/>
                            <field name="company_id"/>
                        </group>
                    </group>
                    <notebook>
                        <page string="Products">
                            <field name="rfq_line_ids">
                                <list editable="bottom">
                                    <field name="product_rfq_id" invisible="1"/>
                                    <field name="product_id"/>
                                    <field name="product_qty"/>
                                    <field name="price_unit"/>
                                </list>
                            </field>
                        </page>
                    </notebook>
                </sheet>
                <footer>
                    <button string="Create RFQ" class="btn btn-primary" type="object" name="action_create_rfq" data-hotkey="q"/>
                    <button string="Create and View RFQ" class="btn btn-primary" type="object" name="action_create_view_rfq" data-hotkey="w"/>
                    <button string="Cancel" class="btn-secondary" special="cancel" data-hotkey="z" />
                </footer>
            </form>
        </field>
    </record>
</odoo>
