from odoo import api, fields, models
from datetime import datetime
from odoo.exceptions import UserError, ValidationError
from odoo.tools import float_round
import logging

_logger = logging.getLogger(__name__)


class SaleOrder(models.Model):
    _inherit = 'sale.order'

    date_order = fields.Datetime(
        default=lambda self: self._get_default_date_order(),
        required=True
    )

    def _check_line_unicity(self, product_id):
        """Override to allow multiple lines with the same product"""
        # Always return True to allow duplicate products (product_id is intentionally unused)
        return True

    def _update_existing_line(self, product_id, quantity, **kwargs):
        """Override to prevent updating existing lines - always create new ones"""
        # Always return False to prevent line updates and force creation of new lines
        # Parameters are intentionally unused as we always return False
        return False

    def _find_existing_line(self, product_id, **kwargs):
        """Override to prevent finding existing lines - always return empty recordset"""
        # Always return empty recordset to prevent line merging
        return self.env['sale.order.line']

    total_cash_payment = fields.Float(string="Total Cash Payment", compute="_compute_total_cash_payment", store=True,
                                      default=0.0)
    total_bank_payment = fields.Float(string="Total Bank Payment", compute="_compute_total_bank_payment", store=True,
                                      default=0.0)
    commission_fees = fields.Float(string="Commission Fees", compute="_compute_commission_fees", store=True, default=0.0)
    
    # Sales Agent Commission Fields
    x_commission_fees = fields.Boolean(string='Commission Fees')
    x_commission_fees_values = fields.Float(string='Commission Fees Value', default=1.5)
    x_commission_fees_id = fields.Many2one('sale.order.line', string='Commission Fees Id', store=True)
    x_sales_agent_id = fields.Many2one('res.partner', string='Sales Agent', 
                                    domain="[('x_is_sales_agent', '=', True)]",
                                    context={'default_x_is_sales_agent': True},
                                    help="Select sales agent (filtered by x_is_sales_agent).")

    def default_get(self, fields_list):
        res = super(SaleOrder, self).default_get(fields_list)
        
        # Check if the context contains 'auto_enable_bool' and set the Boolean field
        if self._context.get('default_x_is_sales_agent'):
            res['x_is_sales_agent'] = True  # Set the Boolean field to True
        
        return res
    
    @api.depends('order_line.x_cash_payment', 'order_line.x_bank_payment', 'order_line.product_id.x_is_bag')
    def _compute_total_cash_payment(self):
        logger = logging.getLogger(__name__)
        for order in self:
            total_cash = 0
            total_cash_service = 0

            for line in order.order_line:
                if not line.product_id.x_is_bag:
                    if line.product_id.type == 'consu':
                        total_cash += line.x_cash_payment * line.product_uom_qty * line.product_id.weight
                    else:
                        total_cash_service += line.x_cash_payment
                else:
                    total_cash_service += line.x_cash_payment
            logger.debug(f"Calculating total cash payment for order {order.id}")
            logger.debug(f"Total cash before service: {total_cash}")
            logger.debug(f"Total cash service: {total_cash_service}")
            logger.debug(f"Total cash payment: {total_cash + total_cash_service}")
            order.total_cash_payment = total_cash + total_cash_service

    @api.depends('order_line.x_cash_payment', 'order_line.x_bank_payment', 'order_line.product_id.x_is_bag')
    def _compute_total_bank_payment(self):
        logger = logging.getLogger(__name__)
        for order in self:
            total_bank = 0
            total_bank_service = 0

            for line in order.order_line:
                if not line.product_id.x_is_bag:
                    if line.product_id.type == 'consu':
                        total_bank += line.x_bank_payment * line.product_uom_qty * line.product_id.weight
                    else:
                        total_bank_service += line.x_bank_payment
                else:
                    total_bank_service += line.x_bank_payment
            logger.debug(f"Calculating total bank payment for order {order.id}")
            logger.debug(f"Total bank before service: {total_bank}")
            logger.debug(f"Total bank service: {total_bank_service}")
            logger.debug(f"Total bank payment: {total_bank + total_bank_service}")
            order.total_bank_payment = total_bank + total_bank_service

    def _get_default_date_order(self):
        return datetime.now()

    @api.depends('order_line.price_subtotal', 'x_commission_fees', 'x_commission_fees_values')
    def _compute_commission_fees(self):
        for order in self:
            product_cost = sum(line.price_subtotal for line in order.order_line if line.product_id.type == 'consu' and not line.product_id.x_is_bag)
            commission_fees = (product_cost * (order.x_commission_fees_values)) if order.x_commission_fees else 0
            order.commission_fees = commission_fees

    def _update_fee_lines(self):
        self.ensure_one()
        
        # Get the commission product or create it if it doesn't exist
        commission_product = self.env['product.product'].search([('name', '=', 'Commission Fee')], limit=1)
        if not commission_product:
            commission_product = self.env['product.product'].create({
                'name': 'Commission Fee',
                'type': 'service',
                'invoice_policy': 'order',
                'purchase_method': 'receive',
            })

        # Get the unit UoM
        uom_unit = self.env.ref('uom.product_uom_unit')

        # Handle Commission Fee Line
        commission_line = self.order_line.filtered(lambda l: l.product_id.id == commission_product.id)
        
        if self.x_commission_fees:
            # Calculate commission amount based on consumable products
            base_amount = sum(line.price_subtotal for line in self.order_line if line.product_id.type == 'consu' and not line.product_id.x_is_bag)
            commission_amount = base_amount * (self.x_commission_fees_values / 100)  # Convert percentage to decimal

            if commission_line:
                # Update existing commission line
                commission_line.write({
                    'product_uom_qty': 1,
                    'product_uom': uom_unit.id,
                    'price_unit': commission_amount,
                    'x_product_rate': commission_amount,
                })
                
                # Update bank/cash payments if needed
                if commission_line.x_bank_payment + commission_line.x_cash_payment != commission_amount:
                    if commission_line.x_cash_payment == 0:
                        commission_line.write({
                            'x_bank_payment': commission_amount,
                            'x_cash_payment': 0
                        })
                    else:
                        if commission_amount > commission_line.x_cash_payment:
                            commission_line.write({
                                'x_bank_payment': commission_amount - commission_line.x_cash_payment
                            })
                        else:
                            commission_line.write({
                                'x_bank_payment': 0,
                                'x_cash_payment': commission_amount
                            })
            else:
                # Create new commission line
                if commission_amount == 0:
                    commission_amount = self.x_commission_fees_values

                self.order_line = [(0, 0, {
                    'order_id': self.id,
                    'product_id': commission_product.id,
                    'name': 'Commission Fee',
                    'product_uom_qty': 1,
                    'product_uom': uom_unit.id,
                    'price_unit': commission_amount,
                    'x_product_rate': commission_amount,
                    'x_bank_payment': commission_amount,
                })]
                commission_line = self.order_line.filtered(lambda l: l.product_id.id == commission_product.id)
                self.x_commission_fees_id = commission_line.id
        elif commission_line:
            # Remove commission line if fees are disabled
            self.x_commission_fees_id = False
            self.order_line = [(2, commission_line.id)]

    @api.onchange('x_commission_fees', 'x_commission_fees_values', 'order_line')
    def _onchange_commission_fees(self):
        if not self.env.context.get('skip_update_fee_lines'):
            self._update_fee_lines()

    def write(self, vals):
        res = super(SaleOrder, self).write(vals)
        
        # Update fee lines if certain fields are changed
        if any(field in vals for field in ['x_commission_fees', 'x_commission_fees_values', 'order_line']):
            for order in self:
                if not self.env.context.get('skip_update_fee_lines'):
                    order.with_context(skip_update_fee_lines=True)._update_fee_lines()
        
        return res

    def _check_payments_within_limit(self, total_payment):
        """
        Validates that neither cash nor bank payment exceeds the total payment limit
        """
        if self.product_id and self.product_id.weight:
            product_total = self.product_uom_qty * self.product_id.weight * self.x_product_rate
            if total_payment > product_total:
                raise UserError("Total payments cannot exceed the product total amount.")


class SaleOrderLine(models.Model):
    _inherit = 'sale.order.line'

    # Override any SQL constraints that might prevent duplicate products
    _sql_constraints = []

    product_uom_qty = fields.Float(
        string='Quantity',
        compute='_compute_product_uom_qty',
        store=True
    )

    line_sequence = fields.Integer(
        string='Sequence',
        default=10,
        help="Unique sequence number for each line of the same product"
    )
    x_bag = fields.Many2one(
            'product.product',
            string='Bag',
            domain=[('x_is_bag', '=', True)],
            help='Select a product that is marked as a bag.'
        )
    x_bag_quantity = fields.Integer(
            string='Bag Quantity',
            store=True,
            help='Quantity of the selected bag product based on the main product quantity.'
        )
    x_product_loose_weight = fields.Float(
            string='Loose Weight',
            store=True,
            help='Weight of the loose product based on the main product quantity.'
        )
    x_product_total_weight = fields.Float(
            string='Total Weight',
            store=True,
            help='Total weight of the product including the loose weight.',
            compute='_compute_x_product_total_weight'
        )
    x_product_rate = fields.Float(string='Product Rate', store=True)
    x_cash_payment = fields.Float(string="Cash Payment", default=0.0)
    x_bank_payment = fields.Float(string="Bank Payment", default=0.0)
    product_uom_qty = fields.Float(string='Quantity', digits=(16, 5), compute='_compute_product_uom_qty', store=True)


    @api.depends('product_uom_qty', 'product_id.weight')
    def _compute_x_product_total_weight(self):
        for line in self:
            if line.product_id.type == 'consu':
                line.x_product_total_weight = line.product_uom_qty * line.product_id.weight
            else:
                line.x_product_total_weight = 0.0

    @api.onchange('x_product_rate', 'product_uom_qty')
    def _onchange_product_rate(self):
        if self.x_product_rate:
            if self.product_id and self.product_id.weight:
                # Convert quintal rate to per kg rate for price_unit
                self.price_unit = self.x_product_rate * (self.product_id.weight / 100)
                
                # Calculate the 1kg rate from quintal rate
                one_kg_rate = self.x_product_rate / 100.0
                
                # Total payment is the sum of cash and bank
                total_payment = self.x_cash_payment + self.x_bank_payment
                
                # If total_payment is zero, set bank payment to one_kg_rate
                if total_payment == 0:
                    self.x_bank_payment = one_kg_rate
                    self.x_cash_payment = 0
                else:
                    # Maintain the ratio
                    cash_ratio = self.x_cash_payment / total_payment if total_payment else 0
                    bank_ratio = self.x_bank_payment / total_payment if total_payment else 1
                    
                    if self.product_id.type == 'consu':
                        # Calculate new values based on the new one_kg_rate
                        self.x_cash_payment = one_kg_rate * cash_ratio
                        self.x_bank_payment = one_kg_rate * bank_ratio
                    elif self.product_id.type == 'service':
                        # For service products, multiply by quantity
                        self.x_cash_payment = one_kg_rate * self.product_uom_qty * cash_ratio
                        self.x_bank_payment = one_kg_rate * self.product_uom_qty * bank_ratio
                
                # Validate payments don't exceed limit
                self._check_payments_within_limit(one_kg_rate)
            else:
                self.price_unit = self.x_product_rate

    @api.onchange('x_cash_payment')
    def _onchange_cash_payment(self):
        if self.env.context.get('skip_onchange'):
            return
            
        total_payment = 0
        if self.product_id.type != 'service':
            # For consumable products, use quintal rate converted to kg
            total_payment = self.x_product_rate / 100.0
        else:
            # For service products
            if self.product_uom_qty != 0:
                total_payment = self.x_product_rate * self.product_uom_qty
            else:
                total_payment = self.x_product_rate
                
        if total_payment > 0:
            # Ensure cash payment doesn't exceed total payment
            if self.x_cash_payment > total_payment:
                self.x_cash_payment = total_payment
            # Bank payment is the remaining amount
            self.x_bank_payment = total_payment - self.x_cash_payment
        
    @api.onchange('x_bank_payment')
    def _onchange_bank_payment(self):
        if self.env.context.get('skip_onchange'):
            return
            
        total_payment = 0
        if self.product_id.type != 'service':
            # For consumable products, use quintal rate converted to kg
            total_payment = self.x_product_rate / 100.0
        else:
            # For service products
            if self.product_uom_qty != 0:
                total_payment = self.x_product_rate * self.product_uom_qty
            else:
                total_payment = self.x_product_rate
                
        if total_payment > 0:
            # Ensure bank payment doesn't exceed total payment
            if self.x_bank_payment > total_payment:
                self.x_bank_payment = total_payment
            # Cash payment is the remaining amount
            self.x_cash_payment = total_payment - self.x_bank_payment
            
    def _check_payments_within_limit(self, total_payment):
        """
        Validates that neither cash nor bank payment exceeds the total payment limit
        """
        if self.x_cash_payment > total_payment:
            self.x_cash_payment = total_payment
        if self.x_bank_payment > total_payment:
            self.x_bank_payment = total_payment

    @api.depends('x_product_loose_weight', 'product_id.weight')
    def _compute_product_uom_qty(self):
        for line in self:
            if line.product_id.type == 'consu':
                base_qty = line.product_uom_qty or 0.0
                if line.x_product_loose_weight > 0:
                    loose_weight_qty = float_round(line.x_product_loose_weight / line.product_id.weight, precision_digits=5)
                    line.product_uom_qty = base_qty + loose_weight_qty
                else:
                    line.product_uom_qty = base_qty

    @api.model_create_multi
    def create(self, vals_list):
        # Handle multiple lines of the same product with different rates
        for vals in vals_list:
            if vals.get('order_id') and vals.get('product_id'):
                existing_lines = self.search([
                    ('order_id', '=', vals['order_id']),
                    ('product_id', '=', vals['product_id'])
                ])
                if existing_lines:
                    max_sequence = max(existing_lines.mapped('line_sequence'))
                    vals['line_sequence'] = max_sequence + 10

        lines = super(SaleOrderLine, self).create(vals_list)
        for line in lines:
            if line.product_id and not line.product_id.x_is_bag:
                line._recompute_bag_qty(line.order_id)
        return lines

    def write(self, vals):
        result = super(SaleOrderLine, self).write(vals)
        for line in self:
            if line.product_id and not line.product_id.x_is_bag:
                line._recompute_bag_qty(line.order_id)
        return result

    def unlink(self):
        """
        Ensure the bag quantity is updated after a product line deletion.
        """
        for line in self:
            if line.product_id and not line.product_id.x_is_bag:
                line._recompute_bag_qty(line.order_id, line.id)
        return super(SaleOrderLine, self).unlink()

    @api.constrains('product_id', 'product_uom_qty', 'x_product_rate')
    def _check_line_validity(self):
        """Ensure valid quantity and rate for each line"""
        for line in self:
            if line.product_uom_qty <= 0:
                raise ValidationError("Quantity must be greater than zero.")
            if line.x_product_rate and line.x_product_rate <= 0:
                raise ValidationError("Product rate must be greater than zero.")

    def _can_be_merged_with(self, other_line):
        """Override to prevent line merging - each line should remain separate"""
        # Always return False to prevent any line merging (other_line is intentionally unused)
        return False

    @api.constrains('product_id', 'order_id')
    def _check_product_location_duplicate(self):
        """Override any constraint that prevents same product with same location"""
        # This method intentionally does nothing to allow duplicate products
        # with same location but different rates
        pass

    def _check_product_duplicate(self):
        """Override to allow duplicate products"""
        # This method intentionally does nothing to allow duplicate products
        pass

    def _validate_product_line(self):
        """Override to allow duplicate products with different rates"""
        # This method intentionally does nothing to allow duplicate products
        pass

    def _recompute_bag_qty(self, order, unlink_line_id=0):
        """
        Recomputes the quantity for the bag type purchase order line.
        If the bag quantity becomes zero, the corresponding purchase order line is removed.
        """
        if not order:
            return

        total_qty = 0
        for line in order.order_line:
            if line.id != unlink_line_id and line.product_id and not line.product_id.x_is_bag:
                total_qty += line.product_uom_qty

        bag_lines = order.order_line.filtered(lambda l: l.product_id.x_is_bag)
        for bag_line in bag_lines:
            if total_qty > 0:
                bag_line.product_uom_qty = total_qty
            else:
                bag_line.unlink()

    @api.onchange('product_id')
    def _onchange_product_id_custom(self):
        """Custom onchange to prevent automatic line merging"""
        # Don't call parent method since it doesn't exist
        # Just allow multiple lines with the same product by not returning any warning
        # This prevents the standard Odoo behavior of merging lines
        if self.product_id:
            # Set basic product information without triggering merge logic
            self.name = self.product_id.name
            self.product_uom = self.product_id.uom_id
        return {}

    @api.onchange('product_id')
    def _onchange_product_details(self):
        if self.product_id:
            self.name = self.product_id.name
            if self.product_id.x_is_bag:
                self.product_uom_qty = sum(
                    line.product_uom_qty
                    for line in self.order_id.order_line
                    if not line.product_id.x_is_bag
                )

    def _compute_price_unit(self):
        for line in self:
            if line.product_id and line.product_id.weight:
                # Convert quintal rate to per kg rate for price_unit
                line.price_unit = line.x_product_rate * (line.product_id.weight / 100)
            else:
                line.price_unit = line.x_product_rate

    def _check_payments_within_limit(self, total_payment):
        if self.product_id and self.product_id.weight:
            product_total = self.product_uom_qty * self.product_id.weight * self.x_product_rate
            if total_payment > product_total:
                raise UserError("Total payments cannot exceed the product total amount.")


# class ProductTemplate(models.Model):
#     _inherit = 'product.template'

#     allow_negative_inventory = fields.Boolean(string='Allow Negative Inventory', default=False)
