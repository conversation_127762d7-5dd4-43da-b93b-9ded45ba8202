<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Extend the purchase.order form view -->
    <record id="purchase_order_form_inherit_admin_date_edit" model="ir.ui.view">
        <field name="name">purchase.order.form.inherit.admin.date.edit</field>
        <field name="model">purchase.order</field>
        <field name="inherit_id" ref="purchase.purchase_order_form"/>
        <field name="arch" type="xml">
            <!-- Add a button to edit dates -->
            <xpath expr="//header" position="inside">
                <button name="action_edit_dates"
                        string="Edit Dates"
                        type="object"
                        groups="ai_admin_date_edit.group_admin_date_edit"
                        disabled="(not can_edit_dates)"
                        class="btn-primary"/>
            </xpath>

            <!-- Make date fields editable for admin users -->
            <xpath expr="//field[@name='date_order']" position="attributes">
                <attribute name="readonly">0</attribute>
                <attribute name="disabled">(not can_edit_dates)</attribute>
            </xpath>

            <xpath expr="//field[@name='date_approve']" position="attributes">
                <attribute name="readonly">0</attribute>
                <attribute name="disabled">(not can_edit_dates)</attribute>
            </xpath>

            <!-- Add the can_edit_dates field (invisible) -->
            <xpath expr="//field[@name='company_id']" position="after">
                <field name="can_edit_dates" invisible="1"/>
            </xpath>

            <!-- Make date_planned editable in order lines - commented out for now as the XPath is not working -->
            <!--
            <xpath expr="//field[@name='order_line']/tree/field[@name='date_planned']" position="attributes">
                <attribute name="readonly">0</attribute>
                <attribute name="disabled">(not parent.can_edit_dates)</attribute>
            </xpath>
            -->
        </field>
    </record>
</odoo>
