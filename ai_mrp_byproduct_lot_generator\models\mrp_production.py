# -*- coding: utf-8 -*-
from odoo import models, fields, api, _
from odoo.exceptions import UserError
import re


class MrpProduction(models.Model):
    _inherit = 'mrp.production'

    def action_generate_byproduct_lots(self):
        """
        Generate lot numbers for byproducts that are tracked by lot or serial
        and assign them to the corresponding stock moves.
        """
        self.ensure_one()
        
        # Get byproduct moves that need lot numbers
        byproduct_moves = self.move_byproduct_ids.filtered(
            lambda m: m.state not in ('done', 'cancel') and 
                      m.product_id.tracking in ('lot', 'serial') and
                      m.quantity > 0
        )
        
        if not byproduct_moves:
            raise UserError(_("There are no byproducts that require lot/serial numbers or all byproducts already have lot/serial numbers assigned."))
        
        lot_vals_list = []
        move_line_vals_list = []
        
        for move in byproduct_moves:
            product = move.product_id
            
            # Determine quantity based on tracking type
            if product.tracking == 'serial':
                # For serial tracked products, we need to create one lot per unit
                qty_to_process = int(move.quantity)
                qty_per_lot = 1.0
            else:
                # For lot tracked products, we create one lot for the entire quantity
                qty_to_process = 1
                qty_per_lot = move.quantity
            
            # Generate lot names based on product name initials
            lot_names = self._generate_lot_names_for_byproduct(product, qty_to_process)
            
            for i, lot_name in enumerate(lot_names):
                # Create lot
                lot_vals = {
                    'name': lot_name,
                    'product_id': product.id,
                    'company_id': self.company_id.id,
                }
                lot_vals_list.append(lot_vals)
                
                # Prepare move line values
                move_line_vals = {
                    'move_id': move.id,
                    'product_id': product.id,
                    'product_uom_id': move.product_uom.id,
                    'location_id': move.location_id.id,
                    'location_dest_id': move.location_dest_id.id,
                    'quantity': qty_per_lot,
                    'lot_name': lot_name,
                    'company_id': self.company_id.id,
                    'production_id': self.id,
                }
                move_line_vals_list.append(move_line_vals)
        
        # Create lots
        lots = self.env['stock.lot'].create(lot_vals_list)
        
        # Create move lines with the generated lots
        self.env['stock.move.line'].create(move_line_vals_list)
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Success'),
                'message': _('%s lot(s) created for byproducts.') % len(lots),
                'sticky': False,
                'type': 'success',
            }
        }
    
    def _generate_lot_names_for_byproduct(self, product, qty):
        """
        Generate lot names for a byproduct based on product name initials and a sequence.
        
        :param product: The product for which to generate lot names
        :param qty: The number of lot names to generate
        :return: A list of lot names
        """
        # Get product name initials
        words = re.findall(r'\b\w', product.name)
        prefix = ''.join(words).upper()
        
        if not prefix:
            prefix = product.name[:2].upper()
        
        if not prefix:
            prefix = 'BP'  # Default prefix for byproducts
        
        # Find the last lot number with this prefix
        last_lot = self.env['stock.lot'].search([
            ('name', 'like', f'{prefix}%'),
            ('product_id', '=', product.id),
        ], order='name DESC', limit=1)
        
        # Determine the starting sequence number
        if last_lot:
            # Extract the numeric part from the last lot name
            match = re.search(r'(\d+)$', last_lot.name)
            if match:
                start_number = int(match.group(1)) + 1
            else:
                start_number = 1
        else:
            start_number = 1
        
        # Generate the lot names
        lot_names = []
        for i in range(qty):
            lot_name = f"{prefix}{(start_number + i):06d}"
            lot_names.append(lot_name)
        
        return lot_names
