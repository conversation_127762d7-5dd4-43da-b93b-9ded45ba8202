from odoo import models, fields, api, _
from odoo.exceptions import UserError
import logging
from odoo.tools import float_compare, float_is_zero

_logger = logging.getLogger(__name__)

class UpdateStockMoveRate(models.TransientModel):
    _name = 'update.stock.move.rate'
    _description = 'Update Stock Move Rate'

    production_id = fields.Many2one('mrp.production', string='Manufacturing Order', readonly=True)
    move_id = fields.Many2one('stock.move', string='Stock Move', required=True,
                             domain="['|', '|', "
                                    "('raw_material_production_id', '=', production_id), "
                                    "('other_material_production_id', '=', production_id), "
                                    "('production_id', '=', production_id)]")
    product_id = fields.Many2one(related='move_id.product_id', string='Product', readonly=True)
    current_rate = fields.Float(string='Current Rate', readonly=True)
    new_rate = fields.Float(string='New Rate', required=True)
    lot_ids = fields.Many2many(related='move_id.lot_ids', string='Lot/Serial Numbers', readonly=True)
    update_valuation = fields.Boolean(string='Update Stock Valuation', default=True,
                                     help='If checked, the stock valuation layers will also be updated')

    @api.onchange('move_id')
    def _onchange_move_id(self):
        """Update the current rate when the move is selected."""
        if self.move_id:
            self.current_rate = self.move_id.actual_cost
            self.new_rate = self.move_id.actual_cost

    def action_update_rate(self):
        """Update the rate of the selected stock move."""
        self.ensure_one()

        if not self.move_id:
            raise UserError(_('Please select a stock move to update.'))

        if self.new_rate <= 0:
            raise UserError(_('The new rate must be greater than zero.'))

        move = self.move_id
        old_rate = move.actual_cost
        production = self.production_id

        _logger.warning("=== Starting rate update for move %s (product: %s) in MO %s ===",
                    move.id, move.product_id.name, production.name)
        _logger.warning("Current values - actual_cost: %s, price_unit: %s",
                    old_rate, move.price_unit)

        try:
            # Update the move's rate
            move.write({
                'actual_cost': self.new_rate,
                'price_unit': self.new_rate,
                'user_modified_rate': True  # Mark as manually modified
            })

            # Force recalculation of total_cost
            move.invalidate_recordset(['total_cost'])

            _logger.info("Successfully updated move rate from %s to %s",
                        old_rate, self.new_rate)

            # Update stock valuation layers if requested
            if self.update_valuation:
                svls = self.env['stock.valuation.layer'].search([('stock_move_id', '=', move.id)])
                _logger.info("Found %s stock valuation layers for move %s", len(svls), move.id)

                for svl in svls:
                    # Calculate new value based on new rate
                    quantity = svl.quantity
                    new_value = quantity * self.new_rate
                    if move.product_id.weight > 0:
                        new_value *= move.product_id.weight

                    _logger.info("Updating SVL %s: unit_cost from %s to %s, value from %s to %s",
                                svl.id, svl.unit_cost, self.new_rate, svl.value, new_value)

                    try:
                        svl.write({
                            'unit_cost': self.new_rate,
                            'value': new_value
                        })
                        _logger.info("Successfully updated SVL %s", svl.id)
                    except Exception as e:
                        _logger.error("Error updating stock valuation layer: %s", str(e), exc_info=True)
                        raise UserError(_("Error updating stock valuation layer: %s") % str(e))

            # Update the manufacturing order's cost analysis using the comprehensive recalculation method
            _logger.info("Using comprehensive recalculation method for MO %s", production.name)
            try:
                # Call the comprehensive recalculation method
                production.recalculate_all_costs()
                _logger.info("Comprehensive recalculation completed successfully for MO %s", production.name)
            except Exception as e:
                _logger.error("Error in comprehensive recalculation: %s", str(e), exc_info=True)
                # Fall back to the old method if the new one fails
                _logger.warning("Falling back to old recalculation method")
                self._update_production_cost_analysis(production, move)

            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': _('Success'),
                    'message': _('Rate successfully updated from %s to %s. Cost analysis has been recalculated.') % (old_rate, self.new_rate),
                    'sticky': False,
                    'type': 'success',
                }
            }

        except Exception as e:
            _logger.error("Error updating rate: %s", str(e), exc_info=True)
            raise UserError(_("Error updating rate: %s") % str(e))

    def _update_production_cost_analysis(self, production, move):
        """Update the manufacturing order's cost analysis after changing a stock move rate."""
        _logger.warning("=== Updating cost analysis for MO %s ===", production.name)

        try:
            # Determine if this is a raw material or other material move
            is_raw_material = bool(move.raw_material_production_id)
            # is_other_material = bool(move.other_material_production_id)

            _logger.info("Move type: raw_material=%s",
                        is_raw_material)

            # Recalculate the appropriate cost field based on move type
            if is_raw_material:
                # Calculate raw material cost
                raw_material_cost = sum(
                    m.total_cost
                    for m in production.move_raw_material_ids.filtered(lambda m: m.state == 'done')
                )
                _logger.info("Recalculated raw_material_cost: %s", raw_material_cost)
                production.write({'raw_material_cost': raw_material_cost})

            if production.move_other_ids:
                # Calculate other material cost
                # Force recalculation of total_cost for all other material moves
                other_moves = production.move_other_ids.filtered(lambda m: m.state == 'done')

                # Log all other material moves before recalculation
                _logger.warning("Other material moves before recalculation:")
                for m in other_moves:
                    _logger.warning("Move ID: %s, Product: %s, actual_cost: %s, product_uom_qty: %s, weight: %s, total_cost: %s",
                                  m.id, m.product_id.name, m.actual_cost, m.product_uom_qty, m.product_id.weight, m.total_cost)

                # Invalidate the cache for all other material moves
                self.env.invalidate_all()

                # Force recalculation of total_cost for each move
                for m in other_moves:
                    # Manually recalculate total_cost
                    calculated_total = m.actual_cost * (m.product_uom_qty * m.product_id.weight)
                    _logger.warning("Manually calculated total_cost for move %s: %s = %s * (%s * %s)",
                                  m.id, calculated_total, m.actual_cost, m.product_uom_qty, m.product_id.weight)

                    # Update the total_cost directly to ensure it's correct
                    m.write({'total_cost': calculated_total})

                    # Force a database update to ensure the values are persisted
                    self.env.cr.commit()

                # Log all other material moves after recalculation
                _logger.warning("Other material moves after recalculation:")
                for m in other_moves:
                    _logger.warning("Move ID: %s, Product: %s, actual_cost: %s, product_uom_qty: %s, weight: %s, total_cost: %s",
                                  m.id, m.product_id.name, m.actual_cost, m.product_uom_qty, m.product_id.weight, m.total_cost)

                # Now sum up the recalculated total_cost values
                # Log each move's total_cost contribution to the sum
                _logger.warning("Summing up total_cost values for other_materials_cost:")
                other_materials_cost = 0
                for m in other_moves:
                    _logger.warning("Adding total_cost for move %s (product: %s): %s",
                                  m.id, m.product_id.name, m.total_cost)
                    other_materials_cost += m.total_cost

                _logger.warning("Final other_materials_cost sum: %s", other_materials_cost)

                # Update both other_materials_cost and other_material_amount to ensure they're in sync
                production.write({
                    'other_materials_cost': other_materials_cost,
                    'other_material_amount': other_materials_cost
                })

                # Log the updated values
                _logger.warning("Updated other_materials_cost: %s", production.other_materials_cost)
                _logger.warning("Updated other_material_amount: %s", production.other_material_amount)

                # Force a database update to ensure the values are persisted
                self.env.cr.commit()

            # Force recalculation of all computed fields by triggering their compute methods
            # This is more reliable than manually recalculating each field

            # First, invalidate the cache for the production record to ensure fresh computation
            production.invalidate_recordset()

            # Trigger weight computation
            if hasattr(production, '_compute_finished_weights'):
                production._compute_finished_weights()
                _logger.info("Triggered _compute_finished_weights")

            # Trigger material costs computation
            if hasattr(production, '_compute_material_costs'):
                production._compute_material_costs()
                _logger.info("Triggered _compute_material_costs")

            # Trigger production cost computation
            if hasattr(production, '_compute_production_cost'):
                production._compute_production_cost()
                _logger.info("Triggered _compute_production_cost")

            # Trigger final costs computation
            if hasattr(production, '_compute_final_costs'):
                production._compute_final_costs()
                _logger.info("Triggered _compute_final_costs")

            # Trigger profit/loss computation if it exists
            if hasattr(production, '_compute_profit_loss'):
                # First, ensure we have the latest total_final_cost
                if hasattr(production, '_compute_final_costs'):
                    production._compute_final_costs()
                    _logger.info("Re-triggered _compute_final_costs before profit_loss calculation")

                # Now compute profit_loss with the updated total_final_cost
                production._compute_profit_loss()
                _logger.info("Triggered _compute_profit_loss")

                # Log the profit/loss values for debugging
                _logger.warning("Profit/Loss values after recalculation:")
                _logger.warning("total_sales_amount: %s", production.total_sales_amount)
                _logger.warning("total_final_cost: %s", production.total_final_cost)
                _logger.warning("profit_loss: %s", production.profit_loss)

                # Force update of profit_loss to ensure it's properly saved
                profit_loss = production.total_sales_amount - production.total_final_cost
                if production.profit_loss != profit_loss:
                    _logger.warning("CRITICAL: profit_loss (%s) does not match calculated value (%s). Fixing...",
                                  production.profit_loss, profit_loss)
                    production.write({
                        'profit_loss': profit_loss
                    })
                    # Force a database update to ensure the values are persisted
                    self.env.cr.commit()
                    _logger.warning("Fixed profit_loss: %s", production.profit_loss)

            # Invalidate cache to ensure UI is updated
            self.env.invalidate_all()

            # Log the final values for debugging
            _logger.warning("=== Final values after cost analysis update for MO %s ===", production.name)
            _logger.warning("other_materials_cost: %s", production.other_materials_cost)
            _logger.warning("other_material_amount: %s", production.other_material_amount)
            _logger.warning("total_material_cost: %s", production.total_material_cost)
            _logger.warning("total_final_cost: %s", production.total_final_cost)

            # Final check: ensure other_material_amount matches other_materials_cost
            if production.other_material_amount != production.other_materials_cost:
                _logger.warning("CRITICAL: other_material_amount (%s) does not match other_materials_cost (%s). Fixing...",
                              production.other_material_amount, production.other_materials_cost)

                # Force update of other_material_amount
                production.write({
                    'other_material_amount': production.other_materials_cost
                })

                # Force a database update to ensure the values are persisted
                self.env.cr.commit()

                _logger.warning("Fixed other_material_amount: %s", production.other_material_amount)

            _logger.warning("=== Cost analysis update completed successfully for MO %s ===", production.name)

        except Exception as e:
            _logger.error("Error updating cost analysis: %s", str(e), exc_info=True)
            raise UserError(_("Error updating cost analysis: %s") % str(e))

    def _recalculate_weights(self, production):
        """Recalculate the weight fields for the manufacturing order."""
        _logger.info("Recalculating weights for MO %s", production.name)

        # Calculate total weight from raw materials
        raw_weight = sum(
            move.product_uom_qty * move.product_id.weight
            for move in production.move_raw_material_ids.filtered(lambda m: m.state not in ('cancel', 'draft'))
        )

        # Calculate total weight from other materials
        other_weight = sum(
            move.product_uom_qty * move.product_id.weight
            for move in production.move_other_ids.filtered(lambda m: m.state not in ('cancel', 'draft'))
        )

        # Calculate total weight
        total_weight = raw_weight + other_weight

        # Calculate weight percentages
        raw_material_weight_percentage = 0.0
        other_material_weight_percentage = 0.0

        if not float_is_zero(total_weight, precision_digits=3):
            raw_material_weight_percentage = (raw_weight / total_weight) * 100
            other_material_weight_percentage = (other_weight / total_weight) * 100

        # Update the production record
        production.write({
            'total_raw_material_weight': raw_weight,
            'total_other_material_weight': other_weight,
            'total_weight': total_weight,
            'raw_material_weight_percentage': raw_material_weight_percentage,
            'other_material_weight_percentage': other_material_weight_percentage
        })

        _logger.info("Updated weights - raw: %s, other: %s, total: %s",
                    raw_weight, other_weight, total_weight)

    def _recalculate_material_costs(self, production):
        """Recalculate the material cost fields for the manufacturing order."""
        _logger.info("Recalculating material costs for MO %s", production.name)

        # Calculate Kachi Raw Material Amount
        kachi_raw_material_amount = production.raw_material_cost

        # Calculate Other Material Amount
        other_material_amount = production.other_materials_cost

        # Calculate Total Material Cost
        total_material_cost = kachi_raw_material_amount + other_material_amount

        # Update the production record
        production.write({
            'kachi_raw_material_amount': kachi_raw_material_amount,
            'other_material_amount': other_material_amount,
            'total_material_cost': total_material_cost
        })

        _logger.info("Updated material costs - Kachi: %s, Other: %s, Total: %s",
                    kachi_raw_material_amount, other_material_amount, total_material_cost)

    def _recalculate_final_costs(self, production):
        """Recalculate the final cost fields for the manufacturing order."""
        _logger.info("Recalculating final costs for MO %s", production.name)

        # Calculate Total Cost with Kachi
        total_cost_with_kachi = production.kachi_raw_material_amount + production.total_additional_cost

        # Calculate Raw Material Grand Amount
        raw_material_grand_amount = total_cost_with_kachi + production.ci_landed_cost

        # Calculate Per KG Cost
        per_kg_cost = 0.0
        if production.total_raw_material_weight and float_compare(production.total_raw_material_weight, 0.001, precision_digits=3) > 0:
            per_kg_cost = raw_material_grand_amount / production.total_raw_material_weight

        # Set Final Raw Material Cost
        final_raw_material_cost = raw_material_grand_amount

        # Calculate Total Final Cost
        total_final_cost = final_raw_material_cost + production.other_material_amount

        # Update the production record
        production.write({
            'total_cost_with_kachi': total_cost_with_kachi,
            'raw_material_grand_amount': raw_material_grand_amount,
            'per_kg_cost': per_kg_cost,
            'final_raw_material_cost': final_raw_material_cost,
            'total_final_cost': total_final_cost
        })

        _logger.info("Updated final costs - Total with Kachi: %s, Grand Amount: %s, Per KG: %s, Final: %s",
                    total_cost_with_kachi, raw_material_grand_amount, per_kg_cost, total_final_cost)
