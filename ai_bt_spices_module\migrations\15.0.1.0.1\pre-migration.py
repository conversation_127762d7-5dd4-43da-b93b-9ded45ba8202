def migrate(cr, version):
    # Add total_raw_material_cost column if it doesn't exist
    cr.execute("""
        DO $$
        BEGIN
            IF NOT EXISTS (
                SELECT 1 
                FROM information_schema.columns 
                WHERE table_name='mrp_production' 
                AND column_name='total_raw_material_cost'
            ) THEN
                ALTER TABLE mrp_production 
                ADD COLUMN total_raw_material_cost numeric DEFAULT 0;
            END IF;
        END $$;
    """)
