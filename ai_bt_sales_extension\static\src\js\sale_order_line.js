/** @odoo-module **/

import { ListController } from "@web/views/list/list_controller";
import { patch } from "@web/core/utils/patch";

// Patch the ListController to prevent automatic line merging in sale orders
patch(ListController.prototype, "ai_bt_sales_extension.sale_order_line", {
    
    /**
     * Override the default behavior to allow multiple lines with the same product
     */
    async _onCellChanged(record, fieldName, value) {
        // Check if we're in a sale order line context
        if (this.props.resModel === 'sale.order.line' && fieldName === 'product_id') {
            // Allow the change without checking for duplicates
            return super._onCellChanged(record, fieldName, value);
        }
        
        // For other models or fields, use the default behavior
        return super._onCellChanged(record, fieldName, value);
    },
    
    /**
     * Override to prevent duplicate product warnings
     */
    async _onFieldChanged(record, fieldName, value) {
        // Check if we're in a sale order line context
        if (this.props.resModel === 'sale.order.line' && fieldName === 'product_id') {
            // Skip duplicate checking for sale order lines
            const result = await super._onFieldChanged(record, fieldName, value);
            
            // Remove any warning about duplicate products
            if (result && result.warning) {
                delete result.warning;
            }
            
            return result;
        }
        
        // For other models or fields, use the default behavior
        return super._onFieldChanged(record, fieldName, value);
    }
});
