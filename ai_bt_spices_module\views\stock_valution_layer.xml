<?xml version="1.0" encoding="UTF-8" ?>
<odoo>
    <data>
        <record id="stock_valuation_layer_tree_extends" model="ir.ui.view">
            <field name="name">stock.valuation.layer.tree.extends</field>
            <field name="model">stock.valuation.layer</field>
            <field name="inherit_id" ref="stock_account.stock_valuation_layer_tree"/>
            <field name="arch" type="xml">
                <xpath expr="//field[@name='lot_id']" position="replace">
                    <field name="lot_id"/>
                </xpath>
                <xpath expr="//field[@name='unit_cost']" position="replace">
                    <field name="unit_cost"/>
                </xpath>
                <xpath expr="//field[@name='quantity']" position="after">
                    <field name="totalweight"  sum="Total Value" optional="show" readonly="1"/>
                </xpath>
            </field>
        </record>
        <record id="view_stock_quant_list_editable_extends" model="ir.ui.view">
            <field name="name">stock.quant.list.editable.extends</field>
            <field name="model">stock.quant</field>
            <field name="inherit_id" ref="stock.view_stock_quant_tree_editable"/>
            <field name="arch" type="xml">
                <!-- <xpath expr="//field[@name='lot_id']" position="after">
                    <field name="lot_avg_cost_per_weight" optional="show"/>
                </xpath> -->
                <xpath expr="//field[@name='inventory_quantity_auto_apply']" position="after">
                    <field name="inventory_quantity_ceil" string="Rounded Quantity" optional="show"/>
                    <field name="create_date" widget="remaining_days"/>
                </xpath>
            </field>
        </record>
    </data>
</odoo>