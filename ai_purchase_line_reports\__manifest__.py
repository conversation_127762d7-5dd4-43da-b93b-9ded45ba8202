{
    'name': 'AI Purchase Line Reports',
    'version': '********.0',
    'category': 'Purchase',
    'summary': 'Enhanced Purchase Order Line Reporting with Custom Fields Support',
    'description': """
        Enhanced Purchase Order Line Reporting
        =====================================

        This module enhances the standard Purchase Order Line views with comprehensive reporting capabilities:

        **Features:**
        * Enhanced list view with all custom fields from existing modules
        * Advanced search filters and group by options
        * Graph and pivot views for visual analytics
        * Smart field visibility controls (optional show/hide)
        * Dedicated reporting menu under Purchase > Reporting
        * Support for all custom fields from ai_bt_spices_module, ai_landed_cost_po_line, etc.

        **Supported Custom Fields:**
        * Barcode scanning fields (barcode_scan)
        * Landed cost fields (landed_cost_amount, landed_cost_ids)
        * Spices module fields (x_product_loose_weight, x_additional_cost, etc.)
        * Auto lot creation fields (lot_ids, line_sequence)
        * Product variant weight fields
        * All standard Purchase Order Line fields

        **Views Available:**
        * Enhanced List View - Comprehensive table with smart field visibility
        * Graph View - Visual analytics and charts
        * Pivot View - Cross-tabulation and analysis
        * Advanced Search - Powerful filtering and grouping

        **Author:** Arihant AI
        **Compatible:** Odoo 18.0 Community Edition
    """,
    'author': 'Arihant AI',
    'website': 'https://www.arihantai.com',
    'license': 'LGPL-3',
    'depends': [
        'purchase',
        'purchase_stock',
        'stock',
        'product',
    ],
    'external_dependencies': {
        'python': [],
    },
    'data': [
        'views/purchase_order_line_report_views.xml',
    ],
    'installable': True,
    'auto_install': False,
    'application': False,
}
