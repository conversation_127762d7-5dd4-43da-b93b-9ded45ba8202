<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Inherit Form View -->
    <record id="view_production_lot_form_inherit" model="ir.ui.view">
        <field name="name">stock.lot.form.inherit</field>
        <field name="model">stock.lot</field>
        <field name="inherit_id" ref="stock.view_production_lot_form"/>
        <field name="arch" type="xml">
            <xpath expr="//field[@name='avg_cost']" position="after">
            <group>
                <field name="avg_cost_per_weight" readonly="1"/>
            </group>
            </xpath>
            <xpath expr="//field[@name='name']" position="after">
                <group>
                <field name="weight" readonly="1"/>
                </group>
            </xpath>
        </field>
    </record>

    <!-- Inherit Tree/List View -->
    <record id="view_production_lot_tree_inherit" model="ir.ui.view">
        <field name="name">stock.lot.tree.inherit</field>
        <field name="model">stock.lot</field>
        <field name="inherit_id" ref="stock.view_production_lot_tree"/>
        <field name="arch" type="xml">
            <field name="create_date" position="before">
                <field name="weight" readonly="1"/>
                <field name="avg_cost_per_weight" readonly="1"/>
            </field>
        </field>
    </record>

      <!-- <record id="view_product_product_stock_list_tree" model="ir.ui.view">
        <field name="name">product.product.stock.list.tree</field>
        <field name="model">product.product.stock.list</field>
        <field name="arch" type="xml">
            <tree>
             
                <field name="product_id"/>
                <field name="quantity"/>
                <xpath expr="//field[@name='quantity']" position="after">
                    <field name="weight" readonly="1"/>
                </xpath>
            </tree>
        </field>
    </record> -->
</odoo>
