# AI Purchase Line Reports

## Overview

This module enhances the standard Odoo Purchase Order Line views with comprehensive reporting capabilities, providing advanced analytics and visualization tools for purchase data analysis.

## Features

### 🎯 **Enhanced Views**
- **Advanced List View** - Comprehensive table with all custom fields
- **Graph View** - Visual analytics and charts
- **Pivot View** - Cross-tabulation and analysis
- **Enhanced Search** - Powerful filtering and grouping

### 📊 **Smart Field Visibility**
- All fields have `optional="show"` or `optional="hide"` attributes
- Users can easily show/hide columns as needed
- Optimized default visibility for common use cases

### 🔍 **Advanced Filtering**
- Status-based filters (Draft, Sent, Purchase, Done, etc.)
- Product type filters (Storable, Consumable, Service, Bags)
- Custom field filters (With Landed Costs, With Lot Numbers, etc.)
- Date-based filters (Today, This Week, This Month, This Year, Late Orders)

### 📈 **Grouping Options**
- Group by Vendor, Product, Category, Status
- Date-based grouping (Order Date, Scheduled Date)
- Company and Currency grouping

### 🎨 **Visual Enhancements**
- Color-coded rows based on status
- Badge widgets for status fields
- Boolean toggle widgets for checkboxes
- Many2many tags for related fields

## Supported Custom Fields

### From `ai_bt_spices_module`:
- `x_product_loose_weight` - Loose Weight
- `x_additional_cost` - Additional Cost
- `x_bag_cost` - Bag Cost
- `x_transport_cost` - Transport Cost
- `x_thekedar_cost` - Thekedar Cost
- `product_is_bag` - Is Bag (Boolean)
- `product_type` - Product Type

### From `ai_landed_cost_po_line`:
- `landed_cost_amount` - Landed Cost Amount
- `landed_cost_ids` - Landed Costs (Many2many)

### From `barcode_scanning_sale_purchase`:
- `barcode_scan` - Lot No

### From `ai_purchase_auto_lot_creation`:
- `line_sequence` - Line Sequence
- `lot_ids` - Lots (One2many)

## Installation

1. Copy the module to your Odoo addons directory
2. Update the app list
3. Install the module from Apps menu

## Usage

### Accessing Reports
Navigate to: **Purchase → Reporting → Purchase Line Analysis**

### Using the Enhanced Views

#### **List View**
- Use column visibility controls to show/hide fields
- Apply filters from the search panel
- Use group by options for data organization
- Export data using standard Odoo export functionality

#### **Graph View**
- Visualize purchase data with bar charts
- Switch between different chart types
- Drill down into specific data points
- Use measures like Subtotal, Quantity, Additional Costs

#### **Pivot View**
- Create cross-tabulation reports
- Drag and drop fields to rows and columns
- Use multiple measures simultaneously
- Export pivot data to Excel

### Recommended Workflows

#### **Vendor Analysis**
1. Group by Vendor
2. Show columns: Product, Quantity, Subtotal, Additional Cost
3. Filter by "This Month" and "Purchase Orders"

#### **Product Performance**
1. Group by Product Category
2. Show columns: Product, Quantity Received, Pending Qty
3. Use Graph view for visual analysis

#### **Cost Analysis**
1. Show all cost-related columns (Subtotal, Additional Cost, Landed Cost)
2. Use Pivot view with Vendor vs Product Category
3. Apply filters for specific time periods

## Technical Details

- **Model**: Uses existing `purchase.order.line` model
- **Views**: Enhanced standard views with additional fields
- **Compatibility**: Odoo 18.0 Community Edition
- **Dependencies**: purchase, purchase_stock, stock, product

## Author

**Arihant AI**  
Website: https://www.arihantai.com  
License: LGPL-3
