from odoo import models, fields, api, _
from odoo.exceptions import UserError, ValidationError
from datetime import datetime
import logging
from odoo.tools import float_round, float_compare

_logger = logging.getLogger(__name__)

class PurchaseOrder(models.Model):
    _inherit = 'purchase.order'

    x_commission_fees = fields.Boolean(string='Commission Fees')
    x_commission_fees_values = fields.Float(string='Commission Fees Value', default=1.5)
    x_market_fees = fields.Boolean(string='Market Fees')
    x_market_fees_values = fields.Float(string='Market Fees Value', default=0.5)
    x_commission_fees_id = fields.Many2one('purchase.order.line', string='Commission Fees Id', store=True)
    x_market_fees_id = fields.Many2one('purchase.order.line', string='Market Fees Id', store=True)
    x_brokerage_fees = fields.Boolean(string='Brokerage Fees')
    x_brokerage_fees_values = fields.Float(string='Brokerage Fees Value', default=0.5)
    x_brokerage_fees_id = fields.Many2one('purchase.order.line', string='Brokerage Fees Id', store=True)
    x_transportation_fees = fields.Boolean(string='Transportation Fees')
    x_transportation_fees_value = fields.Float(string='Transportation Fees Value (per bag)', default=14.0)
    x_thekedar_fees = fields.Boolean(string='Thekedar Fees' , store=True)
    x_thekedar_fees_value = fields.Float(string='Thekedar Fees Value (per bag)', default=5.0)
    # x_transportation_fees_id = fields.Many2one('purchase.order.line', string='Transportation Fees Id', store=True)
    # x_thekedar_fees_id = fields.Many2one('purchase.order.line', string='Thekedar Fees Id', store=True)
    x_thekedar_id = fields.Many2one('res.partner', string='Thekedar',
                                domain="[('x_is_thekedar', '=', True)]",
                                context={'default_x_is_thekedar': True},
                                help="Select thekedar partner (filtered by x_is_thekedar).")
    x_broker_id =   fields.Many2one('res.partner', string='Broker',
                                domain="[('x_is_broker', '=', True)]",
                                context={'default_x_is_broker': True},
                                help="Select Broker partner (filtered by x_is_broker).")
    # # New computed fields for costs
    purchase_cost = fields.Float(
        string="Purchase Cost",
        compute="_compute_purchase_cost",
        store=True,
        help="Sum of product cost, market fees, and commission fees"
    )

    other_cost = fields.Float(
        string="Other Cost",
        compute="_compute_other_cost",
        store=True,
        help="Sum of thekedar fees, transportation fees, and bag fees"
    )

    @api.depends(
        'order_line','order_line.x_bank_payment','order_line.product_id',
        'order_line.product_id.type','order_line.product_id.x_is_bag',
        'x_commission_fees', 'x_commission_fees_values',
        'x_market_fees', 'x_market_fees_values', 'x_brokerage_fees', 'x_brokerage_fees_values'

    )
    def _compute_purchase_cost(self):
        for order in self:
            # Calculate base product cost (excluding fees and bags)
            base_product_cost = sum(
                line.price_subtotal for line in order.order_line
                if line.product_id.type == 'consu' and not line.product_id.x_is_bag
            )

            # Calculate commission fees
            commission_cost = base_product_cost * (order.x_commission_fees_values / 100) if order.x_commission_fees else 0
            brokerage_cost = base_product_cost * (order.x_brokerage_fees_values / 100) if order.x_brokerage_fees else 0
            # Calculate market fees based on bank payments for consumable products
            market_base = sum(
                line.x_bank_payment * line.x_product_total_weight for line in order.order_line
                if line.product_id.type == 'consu' and not line.product_id.x_is_bag
            )
            market_cost = market_base * (order.x_market_fees_values / 100) if order.x_market_fees else 0

            # Total purchase cost includes base product cost and percentage-based fees
            order.purchase_cost = base_product_cost + commission_cost + market_cost + brokerage_cost

            _logger.info(f"Purchase Cost Breakdown:")
            _logger.info(f"Base Product Cost: {base_product_cost}")
            _logger.info(f"Commission Cost: {commission_cost}")
            _logger.info(f"Market Cost: {market_cost}")
            _logger.info(f"Brokerage Cost: {brokerage_cost}")
            _logger.info(f"Total Purchase Cost: {order.purchase_cost}")
    @api.depends('order_line.x_bag_quantity', 'x_transportation_fees', 'x_transportation_fees_value',
                'x_thekedar_fees', 'x_thekedar_fees_value', 'order_line.x_bag.standard_price')
    def _compute_other_cost(self):
        for order in self:
            total_bags = sum(line.x_bag_quantity for line in order.order_line if line.x_bag)
            transportation_cost = (total_bags * order.x_transportation_fees_value) if order.x_transportation_fees else 0
            thekedar_cost = (total_bags * order.x_thekedar_fees_value) if order.x_thekedar_fees else 0
            bag_cost = sum(line.x_bag_quantity * line.x_bag.standard_price for line in order.order_line if line.x_bag)
            order.other_cost = transportation_cost + thekedar_cost + bag_cost

    # Override date_order to use a new default method
    date_order = fields.Datetime(
        default=lambda self: self._get_default_date_order(),
        required=True
    )

    total_cash_payment = fields.Float(string="Total Cash Payment", compute="_compute_total_cash_payment", store=True)
    total_bank_payment = fields.Float(string="Total Bank Payment", compute="_compute_total_bank_payment", store=True)

    total_pending_cash = fields.Float(string="Total Pending Cash Amount", store=True, compute='_compute_pending_cash')
    total_pending_bank = fields.Float(string="Total Pending Bank Amount", store=True, compute='_compute_pending_bank')

    total_cash_paid = fields.Float(string="Total Cash Paid", default=0.0, store=True, tracking=True)
    total_bank_paid = fields.Float(string="Total Bank Paid", default=0.0, store=True, tracking=True)

    @api.depends('x_thekedar_id')
    def _compute_thekear_fees(self):
        for order in self:
            if not order.x_thekedar_id:
                order.x_thekedar_fees = False
                purchase_order_lines = self.env['purchase.order.line'].search([('order_id', '=', order.id)])
                for line in purchase_order_lines:
                    if line.product_id.name == 'Thekedar Cost':
                        _logger.info('Thekedar Cost Line ID : ' + str(line.id))
                        line.unlink()

    @api.onchange('x_thekedar_id')
    def _onchange_thekear_fees(self):
        for order in self:
            if not order.x_thekedar_id:
                order.x_thekedar_fees = False
                purchase_order_lines = self.env['purchase.order.line'].search([('order_id', '=', order.id)])
                for line in purchase_order_lines:
                    if line.product_id.name == 'Thekedar Cost':
                        _logger.info('Thekedar Cost Line ID : ' + str(line.id))
                        line.unlink()

    # @api.onchange('state')
    def _onchange_state(self):
        pass
        # Thekedar Cost has been migrated to Stock picking done in stock.py
        # for order in self:
        #     if order.state == 'purchase':
        #         if order.x_thekedar_id:
        #             purchase_order_lines = self.env['purchase.order.line'].search([('order_id', '=', order.id), ('product_id.name', '=', 'Thekedar Cost')])
        #             _logger.info('purchase_order_lines ID : ' + str(purchase_order_lines))
        #             if purchase_order_lines:
        #                 for line in purchase_order_lines:
        #                     if line.product_id.name == 'Thekedar Cost':
        #                         thekedarcost = self.env['thekedar.cost']
        #                         thekedarcost.create({
        #                             'purchase_order_id': order.id,
        #                             'thekedar_id': order.x_thekedar_id.id,
        #                             'purchase_date': order.date_order,
        #                             'inventory_received_date': order.date_planned,
        #                             'amount_to_be_paid': line.price_subtotal
        #                         })
        #                         _logger.info('Thekedar Cost Created')



    @api.depends('order_line.x_cash_payment', 'order_line.x_bank_payment', 'order_line.product_id.x_is_bag')
    def _compute_total_cash_payment(self):
        for order in self:
            total_cash = 0
            total_cash_service = 0

            # Iterate over each line in the order
            for line in order.order_line:
                if not line.product_id.x_is_bag:
                    _logger.info("_compute_total_cash_payment Product : " + str(line.product_id.name))
                    _logger.info("_compute_total_cash_payment Product : " + str(line.product_id.type))
                    if line.product_id.type == 'consu':
                        # Calculate total for non-service products
                        total_cash += line.x_cash_payment * (line.product_qty * line.product_id.weight)
                        _logger.info("_compute_total_cash_payment Product : " + str(line.product_id.type) + " Cash : " + str(total_cash))
                    else:
                        # Calculate total for service products
                        total_cash_service += line.x_cash_payment
                else:
                    total_cash_service += line.x_cash_payment
            # Update the total cash payment in the order
            order.total_cash_payment = total_cash + total_cash_service
    #
    # @api.onchange('x_thekedar_id')
    # def _onchange_thekear(self):
    #     for order in self:
    #         _logger.info('Thekedar ID : ' + str(order.x_thekedar_id))
    #         if order.x_thekedar_id == False or order.x_thekedar_id == None or order.x_thekedar_id == '' or order.x_thekedar_id == ' ':
    #             _logger.info('Thekedar ID EMPTY : ' + str(order.x_thekedar_id))
    #             order.x_thekedar_fees = False
    @api.depends('total_cash_payment', 'total_cash_paid')
    def _compute_pending_cash(self):
        for order in self:
            order.total_pending_cash = max(0, order.total_cash_payment - order.total_cash_paid)

    @api.depends('total_bank_payment', 'total_bank_paid')
    def _compute_pending_bank(self):
        for order in self:
            order.total_pending_bank = max(0, order.total_bank_payment - order.total_bank_paid)


    @api.depends('order_line.x_cash_payment', 'order_line.x_bank_payment', 'order_line.product_id.x_is_bag')
    def _compute_total_bank_payment(self):
        for order in self:
            # total_bank = sum(line.x_bank_payment * line.product_qty * line.product_id.weight for line in order.order_line if ((not line.product_id.x_is_bag) and (line.product_id.type != 'service')))
            # total_bank_service = sum(line.x_bank_payment for line in order.order_line if ((not line.product_id.x_is_bag) and (line.product_id.type == 'service')))
            # Initialize totals
            total_bank = 0
            total_bank_service = 0
            # Iterate over each line in the order
            for line in order.order_line:
                # Check if the product is not a bag
                _logger.info('_compute_total_bank_payment Checking Product ' + str(line.product_id.name))
                if not line.product_id.x_is_bag:
                    if line.product_id.type == 'consu':
                        # Calculate total for non-service products
                        total_bank += line.x_bank_payment * (line.product_qty * line.product_id.weight)
                        _logger.info(' _compute_total_bank_payment Checking Product Total for Bank' + str(total_bank))
                    else:
                        # Calculate total for service products
                        total_bank_service += line.x_bank_payment
                else:
                    total_bank_service += line.x_bank_payment

            order.total_bank_payment = total_bank + total_bank_service
            _logger.info('_compute_total_bank_payment Updating Bank Total ' + str(total_bank + total_bank_service))

    @api.model
    def _get_default_date_order(self):
        # Get the default date_order from system parameters
        default_date_str = self.env['ir.config_parameter'].sudo().get_param('purchase.default_date_order', default=False)

        # Convert the string to datetime
        if default_date_str:
            try:
                default_date = datetime.strptime(default_date_str, '%Y-%m-%d %H:%M:%S')
            except ValueError:
                raise UserError("The default date format in system parameters is incorrect. Please use 'YYYY-MM-DD HH:MM:SS'.")
            return default_date
        return fields.Datetime.now()  # Fallback to current date if not set

    # @api.model_create_multi
    # def create(self, vals):
    #     # Create the order
    #     order = super(PurchaseOrder, self).create(vals)
    #     # Update the system parameter if date_order is modified
    #     if 'date_order' in vals:
    #         date_order = vals['date_order']
    #         # self.env['ir.config_parameter'].sudo().set_param('purchase.default_date_order', vals['date_order'].strftime('%Y-%m-%d %H:%M:%S'))
    #         # Check if date_order is a string and try to parse it
    #         if isinstance(date_order, str):
    #             try:
    #                 date_order = datetime.strptime(date_order, '%Y-%m-%d %H:%M:%S')
    #             except ValueError:
    #                 raise UserError('The date_order provided is not in the correct format.')
    #
    #         if isinstance(date_order, datetime):
    #             self.env['ir.config_parameter'].sudo().set_param(
    #                 'purchase.default_date_order',
    #                 date_order.strftime('%Y-%m-%d %H:%M:%S')
    #             )
    #         else:
    #             _logger.warning('date_order is not a valid datetime object: %s', date_order)
    #
    #     return order

    @api.model_create_multi
    def create(self, vals_list):
        # First create the records using the parent class method
        orders = super().create(vals_list)

        # Then process the date_order for each created record
        for order in orders:
            if order.date_order:
                try:
                    # Convert string to datetime if needed
                    date_order = order.date_order
                    if isinstance(date_order, str):
                        date_order = fields.Datetime.from_string(date_order)

                    # Update the system parameter
                    if isinstance(date_order, datetime):
                        self.env['ir.config_parameter'].sudo().set_param(
                            'purchase.default_date_order',
                            date_order.strftime('%Y-%m-%d %H:%M:%S')
                        )
                except ValueError:
                    raise UserError('The date_order provided is not in the correct format.')
                except Exception as e:
                    _logger.warning('Error processing date_order: %s', str(e))

        return orders

    def _reorganize_order_lines(self):
        """
        Reorganizes purchase order lines based on product types and creation dates.
        Order:
        1. Consumable products (sorted by create_date)
        2. Service products with x_is_bag=True (sorted by create_date)
        3. Other service products (sorted by create_date)
        """
        self.ensure_one()
        if not self.order_line:
            return

        # Group lines by type
        consu_lines = self.order_line.filtered(lambda l: l.product_id.type == 'consu')
        service_bag_lines = self.order_line.filtered(lambda l: l.product_id.type == 'service' and l.product_id.x_is_bag)
        service_other_lines = self.order_line.filtered(lambda l: l.product_id.type == 'service' and not l.product_id.x_is_bag)

        # Sort each group by create_date
        sorted_consu = consu_lines.sorted(key=lambda l: l.create_date or fields.Datetime.now())
        sorted_service_bag = service_bag_lines.sorted(key=lambda l: l.create_date or fields.Datetime.now())
        sorted_service_other = service_other_lines.sorted(key=lambda l: l.create_date or fields.Datetime.now())

        # Combine all sorted lines
        all_sorted_lines = sorted_consu + sorted_service_bag + sorted_service_other

        # Update sequence numbers
        for idx, line in enumerate(all_sorted_lines, 1):
            if line.sequence != idx:
                if not isinstance(line.id, models.NewId):  # Only update existing records
                    self.env.cr.execute("""
                        UPDATE purchase_order_line
                        SET sequence = %s
                        WHERE id = %s
                    """, (idx, line.id))
                else:
                    line.sequence = idx  # For new records, update directly

    # def write(self, vals):
    #     res = super(PurchaseOrder, self).write(vals)
    #     if 'state' in vals and vals['state'] == 'purchase':
    #         # self.create_landed_cost()
    #         pass
    #     if 'state' not in vals or vals['state'] != 'purchase':
    #         if 'x_commission_fees' in vals or 'x_market_fees' in vals or 'order_line' in vals or 'x_commission_fees_values' in vals or 'x_market_fees_values' in vals:
    #             self._update_fee_lines()
    #     # self.update_order_line_prices()
    #
    #     if 'state' in vals and vals['state'] == 'purchase':
    #         self._onchange_state()
    #
    #     # Check if date_order is being updated
    #     if 'date_order' in vals and vals['date_order'] != self.date_order:
    #         if isinstance(vals['date_order'], str):
    #             date_order = datetime.strptime(vals['date_order'], '%Y-%m-%d %H:%M:%S')
    #             self.env['ir.config_parameter'].sudo().set_param('purchase.default_date_order', date_order.strftime('%Y-%m-%d %H:%M:%S'))
    #         else:
    #             self.env['ir.config_parameter'].sudo().set_param('purchase.default_date_order', vals['date_order'].strftime('%Y-%m-%d %H:%M:%S'))
    #     if 'order_line' in vals:
    #         self._reorganize_order_lines()
    #     return res
    #

    def write(self, vals):
        _logger.info(f"write called with vals: {vals}")
        # Store original values for comparison
        original_date_order = self.date_order

        # Call parent write method first
        res = super().write(vals)

        # Handle state changes
        if vals.get('state') == 'purchase':
            self._onchange_state()

        # Handle date_order updates
        if 'date_order' in vals and vals['date_order'] != original_date_order:
            new_date = vals['date_order']
            if isinstance(new_date, str):
                try:
                    new_date = fields.Datetime.from_string(new_date)
                except ValueError:
                    raise UserError("The default date format in system parameters is incorrect. Please use 'YYYY-MM-DD HH:MM:SS'.")
            if new_date:
                self.env['ir.config_parameter'].sudo().set_param(
                    'purchase.default_date_order',
                    new_date.strftime('%Y-%m-%d %H:%M:%S')
                )

        # Handle fee updates only if not changing to 'purchase' state
        if vals.get('state') != 'purchase':
            fee_related_fields = {'x_commission_fees', 'x_market_fees',
                                  'order_line', 'x_commission_fees_values',
                                  'x_market_fees_values', 'x_trasportation_fees', 'x_trasportation_fees_value', 'x_thekedar_fees', 'x_thekedar_fees_value','x_brokerage_fees', 'x_brokerage_fees_value'}
            if any(field in vals for field in fee_related_fields):
                if 'order_line' in vals:
                    self.with_context(skip_update_fee_lines=True)._update_fee_lines()
                elif 'x_commission_fees' in vals or 'x_commission_fees_values' in vals:
                    self.with_context(skip_update_fee_lines_comission_fees=True)._update_fee_lines()
                elif 'x_market_fees' in vals or 'x_market_fees_values' in vals:
                    self.with_context(skip_update_fee_lines_market_fees=True)._update_fee_lines()
                elif 'x_transportation_fees' in vals or 'x_transportation_fees_value' in vals:
                    self.with_context(skip_update_fee_lines_transportation_fees=True)._update_fee_lines()
                elif 'x_thekedar_fees' in vals or 'x_thekedar_fees_value' in vals:
                    self.with_context(skip_update_fee_lines_thekedar_fees=True)._update_fee_lines()
                elif 'x_brokerage_fees' in vals or 'x_brokerage_fees_value' in vals:
                    self.with_context(skip_update_fee_lines_brokerage_fees=True)._update_fee_lines()




        # Handle order line reorganization
        if 'order_line' in vals:
            self._reorganize_order_lines()

        return res
    # @api.model
    # def create_landed_cost(self):
    #     for order in self:
    #         # Check if any purchase order line includes a product of type 'service'
    #         has_service = any(line.product_type == 'service' for line in order.order_line)
    #         _logger.info(f"Purchase Order {order.name} has service: {has_service}")
    #         if has_service:

    #             existing_journal = self.env['account.journal'].search([
    #                 ('code', '=', order.name),
    #                 ('company_id', '=', order.company_id.id),
    #             ], limit=1)

    #             if existing_journal:
    #                 journal = existing_journal
    #             else:
    #                 journal = self.env['account.journal'].create({
    #                     'name': order.name,
    #                     'type': 'purchase',
    #                     'code': order.name,
    #                     'company_id': order.company_id.id,
    #                 })
    #                 _logger.info(f"Created new journal: {journal.name} and id {journal.id}")
    #             picking = self.env['stock.picking'].create({
    #                 'partner_id': order.partner_id.id,
    #                 'picking_type_id': self.env.ref('stock.picking_type_in').id,
    #                 # Assuming 'stock.picking_type_in' is the correct type
    #                 'location_id': order.picking_type_id.default_location_src_id.id,
    #                 'location_dest_id': order.picking_type_id.default_location_dest_id.id,
    #                 'origin': order.name,
    #                 'move_ids_without_package': [(0, 0, {
    #                     'name': line.product_id.name,
    #                     'product_id': line.product_id.id,
    #                     'product_uom': line.product_uom.id,
    #                     'product_uom_qty': line.product_qty,
    #                     'location_id': order.picking_type_id.default_location_src_id.id,
    #                     'location_dest_id': order.picking_type_id.default_location_dest_id.id,
    #                 }) for line in order.order_line if line.product_type != 'service']
    #                 # Exclude services from stock picking
    #             })
    #             _logger.info(f"Created new stock picking: {picking.name} and id {picking.id}")

    #             # Confirm and validate the picking to set status to 'done'
    #             picking.action_confirm()
    #             picking.button_validate()
    #             _logger.info(f"Stock picking {picking.name} and id {picking.id} set to done")
    #             # Create a new landed cost record

    #             landed_cost = self.env['stock.landed.cost'].create({
    #                 'picking_ids': [(6, 0, [picking.id])],
    #                 'account_journal_id': journal.id,
    #                 'date': fields.Date.today(),
    #             })
    #             _logger.info(f"Created new landed cost: id {landed_cost.id}")

    #             # Add lines to the landed cost
    #             for line in order.order_line:
    #                 if line.product_type == 'service':
    #                     price_unit = line.price_unit  # 4.5% increase for second record
    #                     self.env['stock.landed.cost.lines'].create({
    #                         'cost_id': landed_cost.id,
    #                         'product_id': line.product_id.id,
    #                         'price_unit': price_unit,
    #                         'split_method': line.product_id.split_method_landed_cost,
    #                     })

    #             _logger.info(f"Added lines to landed cost: id {landed_cost.id}")

    #             landed_cost.compute_landed_cost()

    #             # Validate the landed costs
    #             landed_cost.button_validate()

    @api.onchange('order_line')
    def _onchange_payment(self):
        # self._update_fee_lines()

        _logger.info(self.amount_total)
        _logger.info(f"_onchange_payment called")
        if not self.env.context.get('skip_update_fee_lines'):
            self.with_context(skip_update_fee_lines=True)._update_fee_lines()

    @api.onchange('x_commission_fees', 'x_commission_fees_values')
    def _onchange_payment_x_comission_fees(self):
        _logger.info(f"_onchange_payment_x_comission_fees called")
        self.with_context(skip_update_fee_lines_comission_fees=True)._update_fee_lines()

    @api.onchange('x_market_fees', 'x_market_fees_values')
    def _onchange_payment_x_market_fees(self):
        _logger.info(f"_onchange_payment_x_market_fees called")
        self.with_context(skip_update_fee_lines_market_fees=True)._update_fee_lines()

    @api.onchange('x_transportation_fees', 'x_transportation_fees_value')
    def _onchange_payment_x_transportation_fees(self):
        _logger.info(f"_onchange_payment_x_transportation_fees called")
        self.with_context(skip_update_fee_lines_transportation_fees=True)._update_fee_lines()

    @api.onchange('x_thekedar_fees', 'x_thekedar_fees_values')
    def _onchange_payment_x_thekedar_fees(self):
        _logger.info(f"_onchange_payment_x_thekedar_fees called")
        self.with_context(skip_update_fee_lines_thekedar_fees=True)._update_fee_lines()

    @api.onchange('x_brokerage_fees', 'x_brokerage_fees_values')
    def _onchange_payment_x_brokerage_fees(self):
        _logger.info(f"_onchange_payment_x_brokerage_fees called")
        self.with_context(skip_update_fee_lines_brokerage_fees=True)._update_fee_lines()

        # if not self.env.context.get('skip_update_fee_lines_comission_fees'):
        #     self.with_context(skip_update_fee_lines_comission_fees=True)._update_fee_lines()

    def button_confirm(self):
        res = super().button_confirm()
        return res

    def _update_fee_lines(self):
        ProductProduct = self.env['product.product']
        UomUom = self.env['uom.uom']
        PurchaseOrderline = self.env['purchase.order.line']
        _logger.info('xxxxx Upading Fee Lines')
        # Find or create Commission Fee product
        commission_product = ProductProduct.search([('name', '=', 'Commission Fee')], limit=1)
        if not commission_product:
            commission_product = ProductProduct.create({
                'name': 'Commission Fee',
                'type': 'service',
                'purchase_ok': True,
            })

        # Find or create Market Fee product
        market_product = ProductProduct.search([('name', '=', 'Market Fee')], limit=1)
        if not market_product:
            market_product = ProductProduct.create({
                'name': 'Market Fee',
                'type': 'service',
                'purchase_ok': True,
            })

        # Process Transportation Fees
        transportation_product = ProductProduct.search([('name', '=', 'Transportation Cost')], limit=1)
        if not transportation_product:
            transportation_product = ProductProduct.create({
                'name': 'Transportation Cost',
                'type': 'service',
                'purchase_ok': True,
            })

        # Process Thekedar Fees
        thekedar_product = ProductProduct.search([('name', '=', 'Thekedar Cost')], limit=1)
        if not thekedar_product:
            thekedar_product = ProductProduct.create({
                'name': 'Thekedar Cost',
                'type': 'service',
                'purchase_ok': True,
            })
        brokerage_product = ProductProduct.search([('name', '=', 'Brokerage Cost')], limit=1)
        if not brokerage_product:
            brokerage_product = ProductProduct.create({
                'name': 'Brokerage Cost',
                'type': 'service',
                'purchase_ok': True,
            })

        # Find existing fee lines
        commission_line = self.order_line.filtered(lambda l: l.product_id.id == commission_product.id)
        market_line = self.order_line.filtered(lambda l: l.product_id.id == market_product.id)
        brokerage_line = self.order_line.filtered(lambda l: l.product_id.id == brokerage_product.id)
        is_a_bag = self.order_line.filtered(lambda l: l.product_id.x_is_bag == True)
        bag_ids = []
        for bag_line in is_a_bag:
            if bag_line.product_id.id not in bag_ids:
                bag_ids.append(bag_line.product_id.id)
        bag_ids.append(commission_product.id)
        bag_ids.append(market_product.id)
        bag_ids.append(transportation_product.id)
        bag_ids.append(thekedar_product.id)
        # Calculate total amount for existing products (excluding fee lines)
        total_amount = sum(line.price_subtotal for line in self.order_line if
                           line.product_id.id not in bag_ids)

        # total_market_amount = sum(line.price_subtotal * line.x_bank_payment / (line.x_bank_payment + line.x_cash_payment) for line in self.order_line if
        #                    line.product_id.id not in bag_ids and (line.x_bank_payment + line.x_cash_payment) > 0)
        total_market_amount = sum(line.x_bank_payment * (line.product_id.weight * line.product_qty) for line in self.order_line if
                           line.product_id.id not in bag_ids and (line.x_bank_payment + line.x_cash_payment) > 0 and line.product_id.type == 'consu')


        _logger.info('-------------------')
        _logger.info('Total Amount : '+str(total_amount))
        uom_unit = UomUom.search([('name', '=', 'Units')], limit=1)
        if not uom_unit:
            uom_unit = self.env.ref('uom.product_uom_unit')

        # Get the latest date_planned from existing order lines
        # latest_date_planned = max(self.order_line.mapped('date_planned'), default=fields.Datetime.now())

        # Update or create Commission Fee line
        if self.x_commission_fees:
            if self.x_commission_fees_values:
                commission_amount = total_amount * (self.x_commission_fees_values / 100)
                # commission_amount = float(10)  # or use percentage: total_amount * 0.015
                if commission_line:
                    commission_line.write({
                        'product_qty': 1,
                        'product_uom': uom_unit.id,
                        'price_unit': commission_amount,
                        'x_product_rate': commission_amount,
                        # 'x_bank_payment': commission_amount - commission_line.x_cash_payment if commission_line.x_cash_payment else 0,
                        'date_planned': fields.Datetime.now(),
                    })
                    _logger.info(f"Commission Context: {commission_line.name} and {str(self.env.context.get('skip_update_fee_lines'))}")
                    if not self.env.context.get('skip_update_fee_lines'):
                        _logger.info(f"Commission Logic Inside If Statement: {commission_line.name} and {str(self.env.context.get('skip_update_fee_lines'))}")
                        if self.env.context.get('skip_update_fee_lines_comission_fees'):
                            if commission_line.x_cash_payment == 0:
                                _logger.info(f"if not commission_line.x_cash_payment == 0: {commission_line.name} and {str(self.env.context.get('skip_update_fee_lines_comission_fees'))}")
                                commission_line.write({
                                    'x_bank_payment': commission_amount,
                                    'x_cash_payment': 0
                                })
                            else:
                                if commission_amount > commission_line.x_cash_payment:
                                    commission_line.write({
                                        'x_bank_payment': commission_amount - commission_line.x_cash_payment
                                    })
                                else:
                                    _logger.info(f"else: {commission_line.name} and {str(self.env.context.get('skip_update_fee_lines_comission_fees'))}")
                                    commission_line.write({
                                        'x_bank_payment': 0,
                                        'x_cash_payment': commission_amount
                                    })

                else:
                    # bank_payment = 0
                    # if commission_amount == 0:
                    #     bank_payment = 0
                    # else:
                    #     bank_payment = commission_amount
                    self.order_line = [(0, 0, {
                        'sequence': 198,
                        'order_id': self.id,
                        'product_id': commission_product.id,
                        'name': 'Commission Fee',
                        'product_qty': 1,
                        'product_uom': uom_unit.id,
                        'price_unit': commission_amount,
                        'x_product_rate': commission_amount,
                        'x_bank_payment': commission_amount,
                        'date_planned': fields.Datetime.now(),
                    })]

                    commission_line = PurchaseOrderline.search([
                        ('order_id', '=', self.id),
                        ('product_id', '=', commission_product.id)
                    ], limit=1)
                    _logger.info(f"Commission Line: {commission_line.name} and {commission_line.id}")
                    self.x_commission_fees_id = commission_line.id
        elif commission_line:
            self.x_commission_fees_id = False
            # Remove commission line if x_commission_fees is False
            self.order_line = [(2, commission_line.id)]


        if self.x_brokerage_fees:
            if self.x_brokerage_fees_values:
                brokerage_amount = total_amount * (self.x_brokerage_fees_values / 100)
                # brokerage_amount = float(10)  # or use percentage: total_amount * 0.015
                if brokerage_line:
                    brokerage_line.write({
                        'product_qty': 1,
                        'product_uom': uom_unit.id,
                        'price_unit': brokerage_amount,
                        'x_product_rate': brokerage_amount,
                        # 'x_bank_payment': brokerage_amount - brokerage_line.x_cash_payment if brokerage_line.x_cash_payment else 0,
                        'date_planned': fields.Datetime.now(),
                    })
                    if not self.env.context.get('skip_update_fee_lines'):
                        if self.env.context.get('skip_update_fee_lines_brokerage_fees'):
                            if brokerage_line.x_cash_payment == 0:
                                brokerage_line.write({
                                    'x_bank_payment': brokerage_amount,
                                    'x_cash_payment': 0
                                })
                            else:
                                if brokerage_amount > brokerage_line.x_cash_payment:
                                    brokerage_line.write({
                                        'x_bank_payment': brokerage_amount - brokerage_line.x_cash_payment
                                    })
                                else:
                                    brokerage_line.write({
                                        'x_bank_payment': 0,
                                        'x_cash_payment': brokerage_amount
                                    })
                else:
                    self.order_line = [(0, 0, {
                        'order_id': self.id,
                        'product_id': brokerage_product.id,
                        'name': 'Brokerage Fee',
                        'product_qty': 1,
                        'product_uom': uom_unit.id,
                        'price_unit': brokerage_amount,
                        'x_product_rate': brokerage_amount,
                        'x_bank_payment': brokerage_amount,
                        'date_planned': fields.Datetime.now(),
                    })]
                    brokerage_line = PurchaseOrderline.search([
                        ('order_id', '=', self.id),
                        ('product_id', '=', brokerage_product.id)
                    ], limit=1)
                    self.x_brokerage_fees_id = brokerage_line.id
        elif brokerage_line:
            self.x_brokerage_fees_id = False
            # Remove brokerage line if x_brokerage_fees is False
            self.order_line = [(2, brokerage_line.id)]






        # Update or create Market Fee line
        if self.x_market_fees:
            if self.x_market_fees_values:
                market_amount = total_market_amount * (self.x_market_fees_values / 100)
                # market_amount = float(20)  # or use percentage: total_amount * 0.005
                if market_line:
                    market_line.write({
                        'product_qty': 1,
                        'product_uom': uom_unit.id,
                        'price_unit': market_amount,
                        'x_product_rate': market_amount,
                        # 'x_bank_payment': market_amount,
                        'date_planned': fields.Datetime.now(),
                    })
                    if not self.env.context.get('skip_update_fee_lines'):
                        _logger.info(f"Market Context: {self.env.context.get('skip_update_fee_lines_market_fees')} and {str(self.env.context.get('skip_update_fee_lines'))}")
                        if self.env.context.get('skip_update_fee_lines_market_fees'):
                            # market_line.write({
                            #     'x_bank_payment': market_amount,
                            #     'x_cash_payment': 0
                            # })
                            if market_line.x_cash_payment == 0:
                                # _logger.info(f"if not commission_line.x_cash_payment == 0: {market_line.name} and {str(self.env.context.get('skip_update_fee_lines_comission_fees'))}")
                                market_line.write({
                                    'x_bank_payment': market_amount,
                                    'x_cash_payment': 0
                                })
                            else:
                                if market_amount > market_line.x_cash_payment:
                                    market_line.write({
                                        'x_bank_payment': market_amount - market_line.x_cash_payment
                                    })
                                else:
                                    _logger.info(f"else: {market_line.name} and {str(self.env.context.get('skip_update_fee_lines_comission_fees'))}")
                                    market_line.write({
                                        'x_bank_payment': 0,
                                        'x_cash_payment': market_amount
                                    })
                else:
                    self.order_line = [(0, 0, {
                        'order_id': self.id,
                        'product_id': market_product.id,
                        'name': 'Market Fee',
                        'product_qty': 1,
                        'product_uom': uom_unit.id,
                        'price_unit': market_amount,
                        'x_product_rate': market_amount,
                        'x_bank_payment': market_amount,
                        'date_planned': fields.Datetime.now(),
                    })]

                    market_fees_line = PurchaseOrderline.search([
                        ('order_id', '=', self.id),
                        ('product_id', '=', commission_product.id)
                    ], limit=1)
                    self.x_market_fees_id = market_fees_line.id
        elif market_line:
            self.x_market_fees_id = False
            # Remove market line if x_market_fees is False
            self.order_line = [(2, market_line.id)]

        # Bag Products
        is_a_bag = self.order_line.filtered(lambda l: l.product_id.x_is_bag == True)
        total_bag_qty = sum(line.product_qty for line in is_a_bag)  # Calculate total bag quantity

        # # Ensure UoM 'Units' exists
        # uom_unit = UomUom.search([('name', '=', 'Units')], limit=1)
        # if not uom_unit:
        #     uom_unit = self.env.ref('uom.product_uom_unit')

        transportation_line = self.order_line.filtered(lambda l: l.product_id.id == transportation_product.id)

        if self.x_transportation_fees:
            transportation_amount = total_bag_qty * self.x_transportation_fees_value
            if transportation_line:
                transportation_line.write({
                    'product_qty': total_bag_qty,
                    'product_uom': uom_unit.id,
                    'price_unit': self.x_transportation_fees_value,
                    'x_product_rate': self.x_transportation_fees_value,
                    # 'x_bank_payment': transportation_amount,
                    'date_planned': fields.Datetime.now(),
                })
                if transportation_line.x_bank_payment + transportation_line.x_cash_payment != (transportation_line.price_unit * transportation_line.product_qty):
                        # transportation_line.write({
                        #     'x_bank_payment': transportation_amount,
                        #     'x_cash_payment': 0
                        # })
                    if transportation_line.x_cash_payment == 0:
                                # _logger.info(f"if not commission_line.x_cash_payment == 0: {market_line.name} and {str(self.env.context.get('skip_update_fee_lines_comission_fees'))}")
                        transportation_line.write({
                                    'x_bank_payment': transportation_amount,
                                    'x_cash_payment': 0
                                })
                    else:
                        if transportation_amount > transportation_line.x_cash_payment:
                            transportation_line.write({
                                        'x_bank_payment': transportation_amount - transportation_line.x_cash_payment
                                    })
                        else:
                            _logger.info(f"else: {transportation_line.name} and {str(self.env.context.get('skip_update_fee_lines_comission_fees'))}")
                            transportation_line.write({
                                        'x_bank_payment': 0,
                                        'x_cash_payment': transportation_amount
                                    })


                if not self.env.context.get('skip_update_fee_lines'):
                    _logger.info(f"Transportation Context: {self.env.context.get('skip_update_fee_lines_transportation_fees')} and {str(self.env.context.get('skip_update_fee_lines'))}")
                    if self.env.context.get('skip_update_fee_lines_transportation_fees'):
                        if transportation_line.x_cash_payment == 0:
                                # _logger.info(f"if not commission_line.x_cash_payment == 0: {market_line.name} and {str(self.env.context.get('skip_update_fee_lines_comission_fees'))}")
                            transportation_line.write({
                                        'x_bank_payment': transportation_amount,
                                        'x_cash_payment': 0
                                    })
                        else:
                            if transportation_amount > transportation_line.x_cash_payment:
                                transportation_line.write({
                                            'x_bank_payment': transportation_amount - transportation_line.x_cash_payment
                                        })
                            else:
                                _logger.info(f"else: {transportation_line.name} and {str(self.env.context.get('skip_update_fee_lines_comission_fees'))}")
                                transportation_line.write({
                                            'x_bank_payment': 0,
                                            'x_cash_payment': transportation_amount
                                        })
            else:
                if transportation_amount == 0:
                    transportation_amount = self.x_transportation_fees_value

                self.order_line = [(0, 0, {
                    'order_id': self.id,
                    'product_id': transportation_product.id,
                    'name': 'Transportation Cost',
                    'product_qty': total_bag_qty,
                    'product_uom': uom_unit.id,
                    'price_unit': self.x_transportation_fees_value,
                    'x_product_rate': self.x_transportation_fees_value,
                    'x_bank_payment': transportation_amount,
                    'date_planned': fields.Datetime.now(),
                })]
                transportation_line = PurchaseOrderline.search([
                    ('order_id', '=', self.id),
                    ('product_id', '=', transportation_product.id)
                ], limit=1)
                # self.x_transportation_fees_id = transportation_line.id
        elif transportation_line:
            # self.x_transportation_fees_id = False
            self.order_line = [(2, transportation_line.id)]


        thekedar_line = self.order_line.filtered(lambda l: l.product_id.id == thekedar_product.id)

        if self.x_thekedar_fees:
            thekedar_amount = total_bag_qty * self.x_thekedar_fees_value
            if thekedar_line:
                thekedar_line.write({
                    'product_qty': total_bag_qty,
                    'product_uom': uom_unit.id,
                    'price_unit': self.x_thekedar_fees_value,
                    'x_product_rate': self.x_thekedar_fees_value,
                    # 'x_bank_payment': thekedar_amount,
                    'date_planned': fields.Datetime.now(),
                })
                if thekedar_line.x_bank_payment + thekedar_line.x_cash_payment != (thekedar_line.price_unit * thekedar_line.product_qty):
                        # thekedar_line.write({
                        #     'x_bank_payment': thekedar_amount,
                        #     'x_cash_payment': 0
                        # })
                        if thekedar_line.x_cash_payment == 0:
                                # _logger.info(f"if not commission_line.x_cash_payment == 0: {market_line.name} and {str(self.env.context.get('skip_update_fee_lines_comission_fees'))}")
                            thekedar_line.write({
                                        'x_bank_payment': thekedar_amount,
                                        'x_cash_payment': 0
                                    })
                        else:
                            if thekedar_amount > thekedar_line.x_cash_payment:
                                thekedar_line.write({
                                            'x_bank_payment': thekedar_amount - thekedar_line.x_cash_payment
                                        })
                            else:
                                _logger.info(f"else: {thekedar_line.name} and {str(self.env.context.get('skip_update_fee_lines_comission_fees'))}")
                                thekedar_line.write({
                                            'x_bank_payment': 0,
                                            'x_cash_payment': thekedar_amount
                                        })
                if not self.env.context.get('skip_update_fee_lines'):
                    _logger.info(f"Thekedar Context: {self.env.context.get('skip_update_fee_lines_thekedar_fees')} and {str(self.env.context.get('skip_update_fee_lines'))}")
                    if self.env.context.get('skip_update_fee_lines_thekedar_fees'):
                        # thekedar_line.write({
                        #     'x_bank_payment': thekedar_amount,
                        #     'x_cash_payment': 0
                        # })
                        if thekedar_line.x_cash_payment == 0:
                                # _logger.info(f"if not commission_line.x_cash_payment == 0: {market_line.name} and {str(self.env.context.get('skip_update_fee_lines_comission_fees'))}")
                            thekedar_line.write({
                                        'x_bank_payment': thekedar_amount,
                                        'x_cash_payment': 0
                                    })
                        else:
                            if thekedar_amount > thekedar_line.x_cash_payment:
                                thekedar_line.write({
                                            'x_bank_payment': thekedar_amount - thekedar_line.x_cash_payment
                                        })
                            else:
                                _logger.info(f"else: {thekedar_line.name} and {str(self.env.context.get('skip_update_fee_lines_comission_fees'))}")
                                thekedar_line.write({
                                            'x_bank_payment': 0,
                                            'x_cash_payment': thekedar_amount
                                        })

            else:
                if thekedar_amount == 0:
                    thekedar_amount = self.x_thekedar_fees_value
                self.order_line = [(0, 0, {
                    'order_id': self.id,
                    'product_id': thekedar_product.id,
                    'name': 'Thekedar Cost',
                    'product_qty': total_bag_qty,
                    'product_uom': uom_unit.id,
                    'price_unit': self.x_thekedar_fees_value,
                    'x_product_rate': self.x_thekedar_fees_value,
                    'x_bank_payment': thekedar_amount,
                    'date_planned': fields.Datetime.now(),
                })]
                thekedar_line = PurchaseOrderline.search([
                    ('order_id', '=', self.id),
                    ('product_id', '=', thekedar_product.id)
                ], limit=1)
                # self.x_thekedar_fees_id = thekedar_line.id
        elif thekedar_line:
            # self.x_thekedar_fees_id = False
            self.order_line = [(2, thekedar_line.id)]
        self.with_context({}).env.context

    def _compute_unit_price_by_rate(self):
        """
        Computes and updates the unit price based on the product rate and the weight for storable products.
        Formula: (Product Rate / 20) * Product's Weight
        """
        _logger.info('Going to Update Pricing')
        if self.product_id.type == 'consu' and self.product_id.x_is_bag == False:  # Ensure it's a storable product
            product_weight = self.product_id.weight
            if product_weight > 0:
                new_unit_price = (self.x_product_rate / 20) * product_weight
                self.write({'price_unit': new_unit_price})
        else:
            _logger.info('Going to Update Pricing with : ' + str(self.x_product_rate))
            self.write({'price_unit': self.x_product_rate})

class PurchaseOrderLine(models.Model):
    _inherit = 'purchase.order.line'

    product_qty = fields.Float(
        string='Quantity',
        digits=(16, 5),
        store=True,
    )

    # Fields for line coloring
    product_is_bag = fields.Boolean(
        string='Is Bag',
        related='product_id.x_is_bag',
        store=True,
        help='Technical field used for line coloring'
    )
    product_type = fields.Selection(
        string='Product Type',
        related='product_id.type',
        store=True,
        help='Technical field used for line coloring'
    )

    x_product_loose_weight = fields.Float(
        string='Loose Weight',
        store=True,
        tracking=True
    )

    @api.constrains('x_product_loose_weight', 'product_id')
    def _check_loose_weight(self):
        for line in self:
            if line.product_id and line.product_id.type == 'consu':
                if line.x_product_loose_weight >= line.product_id.weight:
                    raise ValidationError(_(
                        "Loose weight (%(loose_weight)s kg) cannot be greater than or equal to "
                        "the product weight (%(product_weight)s kg) for %(product)s",
                        loose_weight=line.x_product_loose_weight,
                        product_weight=line.product_id.weight,
                        product=line.product_id.name
                    ))

    @api.onchange('x_product_loose_weight', 'product_id')
    def _onchange_loose_weight(self):
        if self.product_id and self.product_id.type == 'consu':
            if self.x_product_loose_weight >= self.product_id.weight:
                raise ValidationError(_("Loose weight cannot be greater than product weight (%s kg)", self.product_id.weight))
            # Prevent recursive trigger by using context
            if not self.env.context.get('skip_qty_compute'):
                self.with_context(skip_loose_compute=True).product_qty = (
                    int(self.product_qty or 0) +
                    (self.x_product_loose_weight / self.product_id.weight if self.x_product_loose_weight else 0)
                )

    @api.onchange('product_qty', 'product_id')
    def _onchange_product_qty(self):
        if self.product_id and self.product_id.type == 'consu':
            if self.product_qty and self.product_id.weight and not self.env.context.get('skip_loose_compute'):
                # Calculate loose weight only from the decimal part
                whole_qty = int(self.product_qty)
                decimal_part = self.product_qty - whole_qty
                new_loose_weight = float_round(
                    decimal_part * self.product_id.weight,
                    precision_digits=5
                )
                # Validate loose weight
                if new_loose_weight >= self.product_id.weight:
                    self.product_qty = whole_qty
                    return {
                        'warning': {
                            'title': _('Invalid Quantity'),
                            'message': _(
                                "The decimal part would result in a loose weight "
                                "greater than the product weight (%(weight)s kg)",
                                weight=self.product_id.weight
                            )
                        }
                    }
                self.with_context(skip_qty_compute=True).x_product_loose_weight = new_loose_weight

    @api.model_create_multi
    def create(self, vals_list):
        for vals in vals_list:
            if vals.get('product_id'):
                product = self.env['product.product'].browse(vals['product_id'])
                if product.type == 'consu':
                    if 'x_product_loose_weight' in vals and product.weight:
                        if vals['x_product_loose_weight'] >= product.weight:
                            raise ValidationError(_(
                                "Loose weight (%(loose_weight)s kg) cannot be greater than or equal to "
                                "the product weight (%(product_weight)s kg)",
                                loose_weight=vals['x_product_loose_weight'],
                                product_weight=product.weight
                            ))
                        base_qty = int(vals.get('product_qty', 0))
                        loose_qty = float_round(
                            vals['x_product_loose_weight'] / product.weight,
                            precision_digits=5
                        )
                        vals['product_qty'] = base_qty + loose_qty
                    elif 'product_qty' in vals and product.weight:
                        whole_qty = int(vals['product_qty'])
                        decimal_part = vals['product_qty'] - whole_qty
                        new_loose_weight = float_round(
                            decimal_part * product.weight,
                            precision_digits=5
                        )
                        if new_loose_weight >= product.weight:
                            raise ValidationError(_(
                                "The decimal part would result in a loose weight "
                                "greater than the product weight (%(weight)s kg)",
                                weight=product.weight
                            ))
                        vals['x_product_loose_weight'] = new_loose_weight
        return super().create(vals_list)

    def write(self, vals):
        if 'x_product_loose_weight' in vals and self.product_id.type == 'consu':
            if self.product_id.weight:
                if vals['x_product_loose_weight'] >= self.product_id.weight:
                    raise ValidationError(_(
                        "Loose weight (%(loose_weight)s kg) cannot be greater than or equal to "
                        "the product weight (%(product_weight)s kg) for %(product)s",
                        loose_weight=vals['x_product_loose_weight'],
                        product_weight=self.product_id.weight,
                        product=self.product_id.name
                    ))
                base_qty = int(self.product_qty)
                loose_qty = float_round(
                    vals['x_product_loose_weight'] / self.product_id.weight,
                    precision_digits=5
                )
                vals['product_qty'] = base_qty + loose_qty
        elif 'product_qty' in vals and self.product_id.type == 'consu':
            if self.product_id.weight:
                whole_qty = int(vals['product_qty'])
                decimal_part = vals['product_qty'] - whole_qty
                new_loose_weight = float_round(
                    decimal_part * self.product_id.weight,
                    precision_digits=5
                )
                if new_loose_weight >= self.product_id.weight:
                    raise ValidationError(_(
                        "The decimal part would result in a loose weight "
                        "greater than the product weight (%(weight)s kg) for %(product)s",
                        weight=self.product_id.weight,
                        product=self.product_id.name
                    ))
                vals['x_product_loose_weight'] = new_loose_weight
        return super().write(vals)

    x_bag = fields.Many2one(
        'product.product',
        string='Bag',
        domain=[('x_is_bag', '=', True)],
        help='Select a product that is marked as a bag.'
    )
    x_bag_quantity = fields.Integer(
        string='Bag Quantity',
        store=True,
        help='Quantity of the selected bag product based on the main product quantity.'
    )
    product_qty = fields.Float(string='Quantity', digits=(16, 5), compute='_compute_product_qty', store=True)


    x_product_rate = fields.Float(string='Product Rate', store=True, default=0.0)

    x_cash_payment = fields.Float(string="Cash Payment", default=0.0)
    x_bank_payment = fields.Float(string="Bank Payment", default=0.0)
    # x_product_weight = fields.Float(string="Product Weight", store=True, compute='_compute_product_weight')
    x_product_total_weight = fields.Float(string='Total Weight', compute='_compute_total_weight', store=True)
    x_product_loose_weight = fields.Float(string='Loose Weight', store=True)

    x_additional_cost = fields.Float(
        string='Additional Cost',
        compute='_compute_additional_cost',
        store=True,
        help='Sum of bag cost, transport cost, and thekedar cost for consumable products'
    )
    x_bag_cost = fields.Float(
        string='Bag Cost',
        compute='_compute_bag_cost',
        store=True,
        help='Cost of bags for this line'
    )
    x_transport_cost = fields.Float(
        string='Transport Cost',
        compute='_compute_transport_thekedar_cost',
        store=True,
        help='Transport cost allocated to this line'
    )
    x_thekedar_cost = fields.Float(
        string='Thekedar Cost',
        compute='_compute_transport_thekedar_cost',
        store=True,
        help='Thekedar cost allocated to this line'
    )

    @api.depends('x_bag', 'x_bag_quantity', 'product_qty')
    def _compute_bag_cost(self):
        for line in self:
            if line.x_bag and line.x_bag_quantity and line.product_id.type == 'consu':
                line.x_bag_cost = line.x_bag.standard_price * line.x_bag_quantity
            else:
                line.x_bag_cost = 0.0

    @api.depends('x_bag_quantity', 'order_id.x_transportation_fees', 'order_id.x_transportation_fees_value',
                'order_id.x_thekedar_fees', 'order_id.x_thekedar_fees_value')
    def _compute_transport_thekedar_cost(self):
        for line in self:
            if not line.product_id.type == 'consu':
                line.x_transport_cost = 0.0
                line.x_thekedar_cost = 0.0
                continue

            # Calculate costs based on bag quantity and per-bag rates
            line.x_transport_cost = (
                line.x_bag_quantity * line.order_id.x_transportation_fees_value
                if line.order_id.x_transportation_fees and line.x_bag_quantity
                else 0.0
            )
            line.x_thekedar_cost = (
                line.x_bag_quantity * line.order_id.x_thekedar_fees_value
                if line.order_id.x_thekedar_fees and line.x_bag_quantity
                else 0.0
            )

    @api.depends('x_bag_cost', 'x_transport_cost', 'x_thekedar_cost', 'product_id.type')
    def _compute_additional_cost(self):
        for line in self:
            if line.product_id.type == 'consu':
                line.x_additional_cost = (
                    line.x_bag_cost +
                    line.x_transport_cost +
                    line.x_thekedar_cost
                )
            else:
                line.x_additional_cost = 0.0

    @api.depends('product_qty', 'product_id.weight')
    def _compute_product_weight(self):
        for line in self:
            if line.product_id.type == 'consu':
                line.x_product_weight = line.product_qty * line.product_id.weight
            else:
                line.x_product_weight = 0.0

    @api.depends('product_qty', 'product_id.weight')
    def _compute_total_weight(self):
        for line in self:
            _logger.info('Computing total weight for line: %s', line.id)
            _logger.info('Product: %s, Qty: %s, Weight: %s',
                        line.product_id.name if line.product_id else 'No Product',
                        line.product_qty,
                        line.product_id.weight if line.product_id else 0.0)

            if line.product_id and line.product_qty:
                line.x_product_total_weight = line.product_qty * line.product_id.weight
                _logger.info('Calculated total weight: %s', line.x_product_total_weight)
            else:
                line.x_product_total_weight = 0.0
                _logger.info('Set total weight to 0.0 because: Product exists: %s, Quantity exists: %s',
                            bool(line.product_id), bool(line.product_qty))
            _logger.info('_compute_total_weight : ' + str(line.x_product_total_weight))

    @api.onchange('product_id')
    def _onchange_product_id(self):
        if not self.order_id.partner_id:
            raise UserError(_("Please select a Vendor."))

    @api.onchange('x_product_rate','product_qty')
    def _onchange_product_rate(self):
        if self.x_product_rate and self.product_id.name not in ['Commission Fee', 'Market Fee', 'Transportation Cost', 'Thekedar Cost']:
            # Calculate the 1kg rate
            if self.product_id.type == 'consu':
                one_kg_rate = self.x_product_rate / 20.0
            else:
                one_kg_rate = self.x_product_rate

            # Total payment is the sum of cash and bank
            total_payment = self.x_cash_payment + self.x_bank_payment
            _logger.info('_onchange_product_rate Q total_payment : ' + str(total_payment))
            # If total_payment is zero, set bank payment to one_kg_rate

            if total_payment == 0:
                self.x_bank_payment = one_kg_rate
                self.x_cash_payment = 0
            else:
                # Maintain the ratio
                cash_ratio = self.x_cash_payment / total_payment
                bank_ratio = self.x_bank_payment / total_payment
                if self.product_id.type == 'consu':
                    # Calculate new values based on the new one_kg_rate
                    self.x_cash_payment = one_kg_rate * cash_ratio
                    self.x_bank_payment = one_kg_rate * bank_ratio
                if self.product_id.type == 'service':
                    # Calculate new values based on the new one_kg_rate
                    self.x_cash_payment = one_kg_rate * self.product_qty
                    self.x_bank_payment = one_kg_rate * self.product_qty
            # Validate that neither payment exceeds the 1kg rate
            _logger.info('x_product_rate   x_bank_payment : ' + str(self.x_bank_payment))
            _logger.info('+++++++++++++++++++one kg rate : ' + str(one_kg_rate))
            self._check_payments_within_limit(one_kg_rate)

            self._compute_price_unit()


    @api.onchange('x_cash_payment')
    def _onchange_cash_payment(self):
        if self.env.context.get('skip_onchange'):
            return
        total_payment = 0

        if self.product_id.type != 'service':
            total_payment = self.x_product_rate / 20.0
        else:
            if self.product_qty != 0:
                total_payment = self.x_product_rate * self.product_qty
            else:
                total_payment = self.x_product_rate

        # Ensure x_cash_payment doesn't exceed total_payment
        if self.x_cash_payment > total_payment:
            self.x_cash_payment = total_payment

        # Update bank payment
        self.with_context(skip_update_fee_lines=True).x_bank_payment = total_payment - self.x_cash_payment

        _logger.info('onchange x_bank_payment : ' + str(self.x_bank_payment))

        # Validate payments
        self._check_payments_within_limit(total_payment)

    @api.onchange('x_bank_payment')
    def _onchange_bank_payment(self):
        if self.env.context.get('skip_onchange'):
            return
        total_payment = 0

        if self.product_id.type != 'service':
            total_payment = self.x_product_rate / 20.0
        else:
            # total_payment = self.x_product_rate * self.product_qty
            if self.product_qty != 0:
                total_payment = self.x_product_rate * self.product_qty
            else:
                total_payment = self.x_product_rate

        # Ensure x_bank_payment doesn't exceed total_payment
        if self.x_bank_payment > total_payment:
            self.x_bank_payment = total_payment

        # Update cash payment
        self.with_context(skip_update_fee_lines=True).x_cash_payment = total_payment - self.x_bank_payment


        _logger.info('onchange cash_payment : ' + str(self.x_cash_payment))

        # Validate payments
        self._check_payments_within_limit(total_payment)


    def _check_payments_within_limit(self, one_kg_rate):
        if self.product_id.type == 'consu':
            if self.x_cash_payment > one_kg_rate:
                raise UserError(f"Cash Payment cannot exceed the rate of {one_kg_rate:.2f} per kg.")
            if self.x_bank_payment > one_kg_rate:
                raise UserError(f"Bank Payment cannot exceed the rate of {one_kg_rate:.2f} per kg.")

        else:
            if self.x_cash_payment > one_kg_rate * self.product_qty:
                raise UserError(f"Cash Payment cannot exceed the rate of {one_kg_rate:.2f} per kg.")
            if self.x_bank_payment > one_kg_rate * self.product_qty:
                raise UserError(f"Bank Payment cannot exceed the rate of {one_kg_rate:.2f} per kg.")



    @api.depends('x_product_rate', 'product_id', 'product_id.weight','product_qty')
    def _compute_price_unit(self):
       for line in self:
           # Default to 0
        #    line.price_unit = 0.0
           _logger.info('--------------Calculating price unit for line: %s', line.price_unit)
           # Only proceed if we have both product and rate
           if line.x_product_rate and line.product_id:
               if line.product_id.type == 'consu' and not line.product_id.x_is_bag:
                   product_weight = line.product_id.weight or 0.0
                   if product_weight > 0:
                       line.price_unit = (line.x_product_rate / 20) * product_weight
                       _logger.info('Calculating price: rate=%s, weight=%s -> price=%s',
                                  line.x_product_rate, product_weight, line.price_unit)
               else:
                   line.price_unit = line.x_product_rate  # For bags or services

    @api.model_create_multi
    def create(self, vals_list):
        res = super(PurchaseOrderLine, self).create(vals_list)
        for vals in vals_list:

            if vals.get('x_bag') and vals.get('x_bag_quantity'):
                if self.product_id.x_is_bag:
                    if self.x_bag or self.x_bag_quantity:
                        raise UserError('You can not add Bags on Products of Type Bag.')
                if self.product_id.type == 'service':
                    raise UserError('You can not add Bags on Products of Type Service.')
                self._recompute_bag_qty(res.order_id)

            if vals.get('product_qty')  and self.product_id.type == 'consu' and not self.product_id.x_is_bag:
                _logger.info("calling _compute_price_unit from create method")
                self._compute_price_unit()
        return res

    def write(self, vals):
        res = super(PurchaseOrderLine, self).write(vals)
        # Check if the product is storable and product rate has changed
        _logger.info('PurchaseOrderLine Writing vals: %s', vals)
        if not self.partner_id:
                raise UserError('Please select Vendor.')
        if 'x_product_rate' in vals and self.product_id.type == 'service' and self.product_id.name in ['Commission Fee', 'Market Fee','Transportation Cost','Thekedar Cost']:
            if self.x_cash_payment != 0:
                if vals['x_product_rate'] * self.product_qty >= self.x_cash_payment:
                    self.x_bank_payment = vals['x_product_rate'] * self.product_qty - self.x_cash_payment
                else:
                    if vals['x_product_rate'] * self.product_qty >= self.x_bank_payment:
                        self.x_cash_payment = vals['x_product_rate'] * self.product_qty - self.x_bank_payment
                        # self.x_bank_payment = 0
                    else:
                        self.x_bank_payment = vals['x_product_rate'] * self.product_qty
            else:
                self.x_bank_payment = vals['x_product_rate'] * self.product_qty

        if 'product_qty' in vals and self.product_id.type == 'service' and self.product_id.x_is_bag:
            if vals['product_qty'] >= 1:
                transport_cost = self.env['purchase.order.line'].search([('product_id.name', '=', 'Transportation Cost'), ('order_id', '=', self.order_id.id)])
                # _logger.info('trans_cost: ' + str(transport_cost))
                thekedar_cost = self.env['purchase.order.line'].search([('product_id.name', '=', 'Thekedaar Cost'), ('order_id', '=', self.order_id.id)])
                # _logger.info('thekedar_cost: ' + str(thekedar_cost))
                if transport_cost:
                    transport_cost.write({'product_qty': vals['product_qty']})
                if thekedar_cost:
                    thekedar_cost.write({'product_qty': vals['product_qty']})

        if 'x_bag' in vals or 'x_bag_quantity' in vals:

            if self.product_id.x_is_bag:
                if self.x_bag or self.x_bag_quantity:
                    raise UserError('You can not add Bags on Products of Type Bag.')
            if self.product_id.type == 'service':
                raise UserError('You can not add Bags on Products of Type Service.')
            self._recompute_bag_qty(self.order_id)

        if 'product_qty' in vals and self.product_id.type == 'consu' and not self.product_id.x_is_bag:
            _logger.info("calling _compute_price_unit from write method")

            self._compute_price_unit()


        # Trigger reorganization of order lines
        if self.order_id and not self.env.context.get('skip_reorder'):
            self.order_id.with_context(skip_reorder=True)._reorganize_order_lines()
        return res

    def unlink(self):
        """
        Ensure the bag quantity is updated after a product line deletion.
        """
        for line in self:
            _logger.info('Prinitng Names in unlink event')
            _logger.info(str(line.product_id.name))
            if line.x_bag:
                self._recompute_bag_qty(line.order_id, line.id)
        return super(PurchaseOrderLine, self).unlink()

    def _recompute_bag_qty(self, order, unlink_line_id=0):
        """
        Recomputes the quantity for the bag type purchase order line.
        If the bag quantity becomes zero, the corresponding purchase order line is removed.
        """
        # Find all lines that share the same bag type
        bag_product_ids = []
        for l in order.order_line:
            if l.x_bag:
                if l.x_bag.id not in bag_product_ids:
                    bag_product_ids.append(l.x_bag.id)
        remaining_lines = self.env['purchase.order.line'].search([['order_id','=', order.id],['product_id.x_is_bag','=',True],['product_id','not in',bag_product_ids]])
        remaining_lines.sudo().unlink()

        total_bags = 0



        for bag_product_id in bag_product_ids:
            total_bag_qty = 0
            main_product_lines = self.env['purchase.order.line'].search([
                ('order_id', '=', order.id),
                ('x_bag', '=', bag_product_id),
                ('id','not in',[unlink_line_id])
            ])
            bag_product = self.env['product.product'].search([
                ('id', '=', bag_product_id)
            ])

            for line in main_product_lines:
                total_bag_qty += line.x_bag_quantity

            total_bags += total_bag_qty

            # Find the corresponding bag line
            bag_line = self.env['purchase.order.line'].search([
                ('order_id', '=', order.id),
                ('product_id', '=', bag_product_id)
            ], limit=1)

            if total_bag_qty > 0:
                if bag_line:
                    # Update the existing bag line with the new quantity
                    bag_line.write({
                        'product_qty': total_bag_qty,
                        'x_bank_payment': bag_product.standard_price * total_bag_qty
                    })


                else:
                    # Create a new bag line if it doesn't exist
                    new_line = self.env['purchase.order.line'].create({
                        'order_id': order.id,
                        'product_id': bag_product_id,
                        'product_qty': total_bag_qty,
                        'price_unit': bag_product.standard_price,  # Adjust price if needed
                        'x_product_rate': bag_product.standard_price,
                        # 'x_bank_payment': bag_product.lst_price * total_bag_qty,
                    })
                    if total_bag_qty == 0:
                        new_line.write({
                            'x_bank_payment': bag_product.standard_price
                        })
                    else:
                        new_line.write({
                            'x_bank_payment': bag_product.standard_price * total_bag_qty
                        })

            elif bag_line:
                # Remove the bag line if the quantity is zero after deletion
                bag_line.unlink()

        _logger.info('Total Bags: ' + str(total_bags))

        transport_id = self.env['purchase.order.line'].search([('product_id.name', '=', 'Transportation Cost'), ('order_id', '=', self.order_id.id)], limit=1)
                # _logger.info('trans_cost: ' + str(transport_cost))
        thekedar_id = self.env['purchase.order.line'].search([('product_id.name', '=', 'Thekedaar Cost'), ('order_id', '=', self.order_id.id)], limit=1)
                # _logger.info('thekedar_cost: ' + str(thekedar_cost))
        if transport_id:
                    transport_id.write({'product_qty': total_bags})
        if thekedar_id:
                    thekedar_id.write({'product_qty': total_bags})


        self.order_id._compute_total_bank_payment()
        self.order_id._compute_total_cash_payment()

    def _compute_unit_price_by_rate(self):
        """
        Computes and updates the unit price based on the product rate and the weight for storable products.
        Formula: (Product Rate / 20) * Product's Weight
        """
        _logger.info('Going to Update Pricing')
        if self.product_id.type == 'consu' and self.product_id.x_is_bag == False:  # Ensure it's a storable product
            product_weight = self.product_id.weight
            if product_weight > 0:
                new_unit_price = (self.x_product_rate / 20) * product_weight
                self.write({'price_unit': new_unit_price})
        else:
            _logger.info('Going to Update Pricing with : ' + str(self.x_product_rate))
            self.write({'price_unit': self.x_product_rate})

class ResPartner(models.Model):
    _inherit = 'res.partner'

    x_vendor_stock_location = fields.Many2one('stock.location', string='Vendor Stock Location')
    x_is_thekedar = fields.Boolean(string='Is Thekedar', default=False)
    # is_broker = fields.Boolean(string='Is Broker', default=False)
    x_is_broker = fields.Boolean(string='is Broker')
    def default_get(self, fields_list):
        res = super(ResPartner, self).default_get(fields_list)

        # Check if the context contains 'auto_enable_bool' and set the Boolean field
        if self._context.get('default_x_is_thekedar'):
            res['x_is_thekedar'] = True  # Set the Boolean field to True

        if self._context.get('default_x_is_broker'):
            res['x_is_broker'] = True

        return res

    @api.model
    def create_vendor_warehouse(self):
        """
        Create or retrieve the vendor warehouse location. If a location is already
        created for the vendor, return it. Otherwise, create a new location.
        """
        # Check if the vendor already has a stock location linked
        if self.x_vendor_stock_location:
            return self.x_vendor_stock_location

        # Check if a location already exists for this vendor (based on name, or any other unique criteria)
        domain = [('name', '=', f"{self.name}'s Location")]
        existing_location = self.env['stock.location'].search(domain, limit=1)

        if existing_location:
            return existing_location  # Return existing location if found

        # Create a new location under a parent warehouse location 'VW'
        warehouse_domain = [('location_id.name', '=', 'VW')]
        warehouse = self.env['stock.location'].search(warehouse_domain, limit=1)

        new_location = self.env['stock.location'].create({
            'name': f"{self.name}'s Location",
            'usage': 'internal',
            'location_id': warehouse.id if warehouse else False,  # Adjust this if necessary
        })

        return new_location

    @api.model
    def update_vendor_location(self):
        """
        Update the vendor's stock location if it's not already linked.
        """
        if self.supplier_rank > 0:
            if not self.x_vendor_stock_location:
                location = self.create_vendor_warehouse()
                self.write({
                    'x_vendor_stock_location': location.id
                })

    @api.model_create_multi
    def create(self, vals_list):
        """
        Override the create method to automatically create or link a warehouse location
        when a new vendor is created.
        """
        # Create the records using the super method
        vendors = super(ResPartner, self).create(vals_list)

        # Iterate over the created records and their corresponding `vals`
        for vendor, vals in zip(vendors, vals_list):
            if vals.get('supplier_rank', 0) > 0:  # Only for vendors
                self.update_vendor_location()

        return vendors

    def write(self, vals):
        """
        Override the write method to ensure the vendor warehouse location is updated or linked correctly
        when a partner is made a vendor.
        """
        res = super(ResPartner, self).write(vals)
        if 'supplier_rank' in vals:  # Check only if supplier_rank is modified
            for vendor in self:
                if vendor.supplier_rank > 0:
                    self.update_vendor_location()
                else:
                    # Optionally handle the case where supplier_rank is set to 0
                    # For example, you could choose to unlink the vendor location
                    if vendor.x_vendor_stock_location:
                        # You could choose to unlink or keep it based on your business logic
                        # vendor.x_vendor_stock_location.unlink()
                        pass
        return res

    # @api.model
    # def create(self, vals):
    #     vendor = super(ResPartner, self).create(vals)
    #     if vals.get('supplier_rank', 0) > 0:  # Only for vendors
    #         location = self.create_vendor_warehouse(vendor)
    #         self.update_vendor_location(vendor, location)
    #     return vendor

    # def write(self, vals):
    #     res = super(ResPartner, self).write(vals)
    #     if 'supplier_rank' in vals:  # Only for vendors
    #         for vendor in self:
    #             location = self.create_vendor_warehouse(vendor)
    #             self.update_vendor_location(vendor, location)
    #     return res
    # @api.model
    # def create_vendor_warehouse(self, vendor):
    #     """
    #     Create a new warehouse location for the vendor.
    #     """
    #     domain = [('location_id.name', '=', 'VW')]
    #     warehouse = self.env['stock.location'].search(domain, limit=1)
    #     new_location = self.env['stock.location'].create({
    #         'name': f"{vendor.name}'s Location",
    #         'usage': 'internal',
    #         'location_id': warehouse.id,
    #     })
    #     return new_location

    # @api.model
    # def update_vendor_location(self, vendor, location):
    #     """
    #     Update the vendor's stock location.
    #     """
    #     vendor.write({
    #         'x_vendor_stock_location': location.id
    #     })


class ProductTemplate(models.Model):
    _inherit = 'product.template'

    x_is_bag = fields.Boolean(string='Is Bag?', help='Indicates if the product is a bag.')
    x_bag_weight = fields.Float(string='Bag Weight')
    raw_material = fields.Boolean(string='Raw Material', default=False)
    other_material = fields.Boolean(string='Other Material', default=False)

    @api.model
    def create(self, vals):
        _logger.info('Creating product template with vals: %s', vals)
        return super(ProductTemplate, self).create(vals)

    def write(self, vals):
        _logger.info('Writing to product template %s with vals: %s', self.name, vals)

        # Check if raw_material or other_material fields are being updated
        if ('raw_material' in vals or 'other_material' in vals) and not self.env.context.get('syncing_from_variant'):
            # Get the current record's product variants
            product_variants = self.env['product.product'].search([('product_tmpl_id', 'in', self.ids)])
            _logger.info(f"Syncing raw_material/other_material to {len(product_variants)} variants")

            # If raw_material is being set to True, ensure all variants have it set to True
            if 'raw_material' in vals and vals['raw_material']:
                _logger.info(f"Setting raw_material=True for all variants of template {self.name}")
                product_variants.with_context(syncing_from_template=True).write({'raw_material': True})
            # If raw_material is being set to False, only set variants to False if no other variant has it True
            elif 'raw_material' in vals and not vals['raw_material']:
                # Only propagate False if we're not in a sync operation from a variant
                product_variants.with_context(syncing_from_template=True).write({'raw_material': False})

            # If other_material is being set to True, ensure all variants have it set to True
            if 'other_material' in vals and vals['other_material']:
                _logger.info(f"Setting other_material=True for all variants of template {self.name}")
                product_variants.with_context(syncing_from_template=True).write({'other_material': True})
            # If other_material is being set to False, only set variants to False if no other variant has it True
            elif 'other_material' in vals and not vals['other_material']:
                # Only propagate False if we're not in a sync operation from a variant
                product_variants.with_context(syncing_from_template=True).write({'other_material': False})

        return super(ProductTemplate, self).write(vals)

    @api.model
    def sync_material_fields_for_all_products(self):
        """
        Synchronize ONLY raw_material and other_material fields between templates and variants
        for all existing products in the database.
        This method can be called manually or via a scheduled action to fix existing data.

        IMPORTANT: This method only touches the raw_material and other_material fields.
        It does not modify any other fields or functionality.
        """
        _logger.info("Starting synchronization of ONLY raw_material and other_material fields for all products")

        # Get all product templates
        templates = self.search([])
        templates_count = len(templates)
        _logger.info(f"Found {templates_count} product templates to process")

        # Process in batches to avoid memory issues
        batch_size = 100
        for i in range(0, templates_count, batch_size):
            batch = templates[i:i+batch_size]
            _logger.info(f"Processing batch {i//batch_size + 1}/{(templates_count//batch_size) + 1} ({len(batch)} templates)")

            for template in batch:
                variants = template.product_variant_ids

                if not variants:
                    continue

                # Check raw_material values
                # If either template OR any variant has True, all should be True
                template_raw_material = template.raw_material
                variants_raw_material = variants.mapped('raw_material')
                any_raw_material_true = template_raw_material or any(variants_raw_material)

                if any_raw_material_true:
                    # If any is True, set all to True
                    _logger.info(f"Setting raw_material=True for template {template.name} (ID: {template.id}) and all its variants")

                    # Update template if needed
                    if not template_raw_material:
                        template.with_context(syncing_from_variant=True).write({'raw_material': True})

                    # Update variants if needed
                    variants_to_update = variants.filtered(lambda v: not v.raw_material)
                    if variants_to_update:
                        variants_to_update.with_context(syncing_from_template=True).write({'raw_material': True})
                else:
                    # If all are False, ensure consistency
                    if template_raw_material:
                        template.with_context(syncing_from_variant=True).write({'raw_material': False})

                    variants_to_update = variants.filtered(lambda v: v.raw_material)
                    if variants_to_update:
                        variants_to_update.with_context(syncing_from_template=True).write({'raw_material': False})

                # Check other_material values
                # If either template OR any variant has True, all should be True
                template_other_material = template.other_material
                variants_other_material = variants.mapped('other_material')
                any_other_material_true = template_other_material or any(variants_other_material)

                if any_other_material_true:
                    # If any is True, set all to True
                    _logger.info(f"Setting other_material=True for template {template.name} (ID: {template.id}) and all its variants")

                    # Update template if needed
                    if not template_other_material:
                        template.with_context(syncing_from_variant=True).write({'other_material': True})

                    # Update variants if needed
                    variants_to_update = variants.filtered(lambda v: not v.other_material)
                    if variants_to_update:
                        variants_to_update.with_context(syncing_from_template=True).write({'other_material': True})
                else:
                    # If all are False, ensure consistency
                    if template_other_material:
                        template.with_context(syncing_from_variant=True).write({'other_material': False})

                    variants_to_update = variants.filtered(lambda v: v.other_material)
                    if variants_to_update:
                        variants_to_update.with_context(syncing_from_template=True).write({'other_material': False})

            # Commit after each batch to avoid long-running transactions
            self.env.cr.commit()

        _logger.info("Completed synchronization of ONLY raw_material and other_material fields for all products")
        return True

class ProductProduct(models.Model):
    _inherit = 'product.product'

    raw_material = fields.Boolean(string='Raw Material', default=False)
    other_material = fields.Boolean(string='Other Material', default=False)
    total_weight = fields.Float(string='Total Weight' , compute='_compute_total_weight', store=True, help='Total weight of the product based on free to use quantity available')

    @api.depends('weight', 'qty_available')
    def _compute_total_weight(self):
        for product in self:
            product.total_weight = product.weight * product.qty_available

    @api.model
    def create(self, vals):
        _logger.info('Creating product with vals: %s', vals)
        # When creating a variant, sync raw_material and other_material from template if not explicitly set
        if 'product_tmpl_id' in vals:
            template = self.env['product.template'].browse(vals['product_tmpl_id'])
            if template:
                if 'raw_material' not in vals:
                    vals['raw_material'] = template.raw_material
                if 'other_material' not in vals:
                    vals['other_material'] = template.other_material

        return super(ProductProduct, self).create(vals)

    def write(self, vals):
        # Only log writes if not from manufacturing order
        if not self.env.context.get('from_manufacturing_order'):
            _logger.info('Writing to product %s with vals: %s', self.name, vals)

        # Check if raw_material or other_material fields are being updated
        if ('raw_material' in vals or 'other_material' in vals) and not self.env.context.get('syncing_from_template'):
            # Get the product templates
            templates = self.mapped('product_tmpl_id')

            for template in templates:
                # Get all variants of this template
                all_variants = template.product_variant_ids
                # Get variants from this template that are in the current recordset
                current_variants = self.filtered(lambda p: p.product_tmpl_id.id == template.id)

                # If raw_material is being set to True, propagate to template and all other variants
                if 'raw_material' in vals and vals['raw_material']:
                    _logger.info(f"Setting raw_material=True for template {template.name} and all its variants")

                    # Update template
                    template.with_context(syncing_from_variant=True).write({'raw_material': True})

                    # Update other variants that are not in the current recordset
                    other_variants = all_variants - current_variants
                    if other_variants:
                        other_variants.with_context(syncing_from_template=True).write({'raw_material': True})

                # If raw_material is being set to False, only propagate if no other variant has it True
                elif 'raw_material' in vals and not vals['raw_material']:
                    # Check if any other variant (not in current recordset) has raw_material=True
                    other_variants = all_variants - current_variants
                    any_other_raw_material = other_variants.filtered(lambda v: v.raw_material)

                    if not any_other_raw_material:
                        # If no other variant has raw_material=True, update template
                        template.with_context(syncing_from_variant=True).write({'raw_material': False})

                # If other_material is being set to True, propagate to template and all other variants
                if 'other_material' in vals and vals['other_material']:
                    _logger.info(f"Setting other_material=True for template {template.name} and all its variants")

                    # Update template
                    template.with_context(syncing_from_variant=True).write({'other_material': True})

                    # Update other variants that are not in the current recordset
                    other_variants = all_variants - current_variants
                    if other_variants:
                        other_variants.with_context(syncing_from_template=True).write({'other_material': True})

                # If other_material is being set to False, only propagate if no other variant has it True
                elif 'other_material' in vals and not vals['other_material']:
                    # Check if any other variant (not in current recordset) has other_material=True
                    other_variants = all_variants - current_variants
                    any_other_other_material = other_variants.filtered(lambda v: v.other_material)

                    if not any_other_other_material:
                        # If no other variant has other_material=True, update template
                        template.with_context(syncing_from_variant=True).write({'other_material': False})

        return super(ProductProduct, self).write(vals)

class BrokerCost(models.Model):
    _name = 'broker.cost'
    _description = 'Broker Costs Tracking'

    # Linking with Purchase Order
    purchase_order_id = fields.Many2one('purchase.order', string="Purchase Order", required=True)

    # Linking Thekedar (filtered based on res.partner with x_is_broker)
    broker_id = fields.Many2one('res.partner', string="Broker", domain=[('x_is_broker', '=', True)],
                                  context={'default_x_is_broker': True}
                                  , required=True)

    # Purchase Date (Auto-filled from Purchase Order)
    purchase_date = fields.Datetime(related='purchase_order_id.date_approve', string="Purchase Date", readonly=True)

    # Inventory Received Date (Manually entered by the user or auto-fill from stock.picking)
    inventory_received_date = fields.Date(string="Inventory Received Date")

    # Amount to be Paid (Fetched from Purchase Order Line with Thekedar-related costs)
    amount_to_be_paid = fields.Float(string="Amount to be Paid", compute='_compute_amount_to_be_paid', store=True)

    # Amount Paid (Manually entered by the user)
    amount_paid = fields.Float(string="Amount Already Paid", default=0.0)

    due_amount = fields.Float(string="Due Amount", compute='_compute_due_amount', store=True)

    # Function to calculate the amount to be paid from the Purchase Order Line
    @api.depends('purchase_order_id', 'purchase_order_id.order_line')
    def _compute_amount_to_be_paid(self):
        for record in self:
            total_broker_cost = 0.0
            for line in record.purchase_order_id.order_line:
                if line.product_id.name == 'Broker Cost':  # Assuming Thekedar-related products are flagged with this
                    total_broker_cost += line.price_subtotal
            record.amount_to_be_paid = total_broker_cost - record.amount_paid

    @api.depends('amount_to_be_paid', 'amount_paid')
    def _compute_due_amount(self):
        for record in self:
            record.due_amount = record.amount_to_be_paid - record.amount_paid