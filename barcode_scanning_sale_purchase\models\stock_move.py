# -*- coding: utf-8 -*-
################################################################################
#
#    Cybrosys Technologies Pvt. Ltd.
#
#    Copyright (C) 2024-TODAY Cybrosys Technologies(<https://www.cybrosys.com>).
#    Author:  <PERSON><PERSON> (<EMAIL>)
#
#    You can modify it under the terms of the GNU AFFERO
#    GENERAL PUBLIC LICENSE (AGPL v3), Version 3.
#
#    This program is distributed in the hope that it will be useful,
#    but WITHOUT ANY WARRANTY; without even the implied warranty of
#    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
#
#    You should have received a copy of the GNU AFFERO GENERAL PUBLIC LICENSE
#    (AGPL v3) along with this program.
#    If not, see <http://www.gnu.org/licenses/>.
#
################################################################################
from odoo import api, fields, models
import logging

_logger = logging.getLogger(__name__)


class StockMove(models.Model):
    """Inherited the model for add field for barcode."""
    _inherit = "stock.move"

    barcode_scan = fields.Char(string='Lot No',
                               help="Enter the lot number or scan product barcode. "
                                    "The system will find the product while preserving the lot number value.")









    @api.onchange('barcode_scan')
    def _onchange_barcode_scan(self):
        """Onchange function for searching product using their barcode or lot/serial number
        while preserving the entered lot number value"""
        # Get the parent product ID from MRP context
        parent_product_id = False
        if self.raw_material_production_id and self.raw_material_production_id.product_id:
            parent_product_id = self.raw_material_production_id.product_id.id
        elif self.other_material_production_id and self.other_material_production_id.product_id:
            parent_product_id = self.other_material_production_id.product_id.id

        if not self.barcode_scan:
            # Clear product selection when barcode is cleared
            self.product_id = False
            # Return domain that excludes parent product if in MRP context
            if parent_product_id:
                return {'domain': {'product_id': [('id', '!=', parent_product_id)]}}
            else:
                return {'domain': {'product_id': []}}

        # First try to find product by barcode
        product = self.env['product.product'].search(
            [('barcode', '=', self.barcode_scan)], limit=1)

        if product:
            # Check if the product is not the parent product in MRP context
            if parent_product_id and product.id == parent_product_id:
                # Show warning that the product is the same as the parent product
                return {
                    'warning': {
                        'title': 'Invalid Product',
                        'message': 'You cannot select the same product as the manufacturing product.'
                    }
                }
            else:
                self.product_id = product
                # Return domain that excludes parent product if in MRP context
                if parent_product_id:
                    return {'domain': {'product_id': [('id', '!=', parent_product_id)]}}
                else:
                    return {'domain': {'product_id': []}}

        # If no product found by barcode, try to find by lot/serial number
        lots = self.env['stock.lot'].search([('name', '=', self.barcode_scan)])
        if lots:
            products = lots.mapped('product_id')

            # Filter out parent product if needed
            filtered_products = products.filtered(lambda p: p.id != parent_product_id) if parent_product_id else products

            if filtered_products:
                # Set the first product
                self.product_id = filtered_products[0]

                # Enhanced logging for debugging
                _logger.info("Found products for lot %s: %s", self.barcode_scan, filtered_products.ids)

                # If this is a move line that can have a lot assigned, assign it
                if hasattr(self, 'move_line_ids') and self.move_line_ids:
                    matching_lot = lots.filtered(lambda l: l.product_id.id == filtered_products[0].id)
                    if matching_lot:
                        for move_line in self.move_line_ids:
                            if not move_line.lot_id and move_line.product_id.id == filtered_products[0].id:
                                move_line.lot_id = matching_lot[0]
                                break

                # Check if multiple products found
                if len(filtered_products) > 1:
                    # Multiple products found, open wizard for selection
                    return self._open_product_selection_wizard(filtered_products)
                else:
                    # Single product found, return domain
                    return {'domain': {'product_id': [('id', 'in', filtered_products.ids)]}}

        # No products found, return domain that excludes parent product if in MRP context
        if parent_product_id:
            return {'domain': {'product_id': [('id', '!=', parent_product_id)]}}
        else:
            return {'domain': {'product_id': []}}

    def _open_product_selection_wizard(self, products):
        """Open wizard to select product when multiple products are found for the same lot"""
        self.ensure_one()

        # Create wizard record
        wizard = self.env['product.selection.wizard'].create({
            'lot_number': self.barcode_scan,
            'source_model': self._name,
            'source_record_id': self.id,
        })

        # Create wizard lines for each product
        wizard_lines = []
        for product in products:
            wizard_lines.append((0, 0, {
                'product_id': product.id,
                'is_selected': False,
            }))

        wizard.line_ids = wizard_lines

        # Return action to open wizard
        return {
            'name': f'Select Product for Lot {self.barcode_scan}',
            'type': 'ir.actions.act_window',
            'res_model': 'product.selection.wizard',
            'res_id': wizard.id,
            'view_mode': 'form',
            'target': 'new',
            'context': {
                'default_lot_number': self.barcode_scan,
                'default_source_model': self._name,
                'default_source_record_id': self.id,
            }
        }

    def action_open_product_wizard(self):
        """Open wizard to select product when multiple products are found for the same lot"""
        self.ensure_one()

        # Check if the parent MO is in an editable state
        parent_mo = None
        if self.raw_material_production_id:
            parent_mo = self.raw_material_production_id
        elif self.other_material_production_id:
            parent_mo = self.other_material_production_id
        elif self.production_id:
            parent_mo = self.production_id

        if parent_mo and parent_mo.state not in ('draft', 'confirmed', 'progress'):
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'Cannot Edit',
                    'message': f'Cannot modify products when Manufacturing Order is in {parent_mo.state} state.',
                    'type': 'warning',
                }
            }

        if not self.barcode_scan:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'No Lot Number',
                    'message': 'Please enter a lot number first.',
                    'type': 'warning',
                }
            }

        # Find products for this lot number
        lots = self.env['stock.lot'].search([('name', '=', self.barcode_scan)])
        if not lots:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'No Lots Found',
                    'message': f'No lots found for lot number {self.barcode_scan}',
                    'type': 'warning',
                }
            }

        products = lots.mapped('product_id')
        if not products:
            return {
                'type': 'ir.actions.client',
                'tag': 'display_notification',
                'params': {
                    'title': 'No Products Found',
                    'message': f'No products found for lot number {self.barcode_scan}',
                    'type': 'warning',
                }
            }

        # Create wizard record
        wizard = self.env['product.selection.wizard'].create({
            'lot_number': self.barcode_scan,
            'source_model': self._name,
            'source_record_id': self.id,
        })

        # Create wizard lines for each product
        wizard_lines = []
        for product in products:
            wizard_lines.append((0, 0, {
                'product_id': product.id,
                'is_selected': False,
            }))

        wizard.line_ids = wizard_lines

        # Return action to open wizard
        return {
            'name': f'Select Product for Lot {self.barcode_scan}',
            'type': 'ir.actions.act_window',
            'res_model': 'product.selection.wizard',
            'res_id': wizard.id,
            'view_mode': 'form',
            'target': 'new',
        }