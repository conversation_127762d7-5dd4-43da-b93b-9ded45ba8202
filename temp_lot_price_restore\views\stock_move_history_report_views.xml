<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Stock Move History Report List View (replacing Tree View in Odoo 18) -->
    <record id="view_stock_move_history_report_list" model="ir.ui.view">
        <field name="name">stock.move.history.report.list</field>
        <field name="model">stock.move.history.report</field>
        <field name="arch" type="xml">
            <list>
                <header>
                    <button name="populate_report" string="Populate Report" type="object" class="oe_highlight"/>
                </header>
                <field name="reference"/>
                <field name="date" string="Scheduled Date"/>
                <field name="scheduled_date" string="Original Scheduled Date" optional="hide"/>
                <field name="picking_type_code"/>
                <field name="location_id"/>
                <field name="location_dest_id"/>
                <field name="warehouse_id"/>
                <field name="product_id"/>
                <field name="product_categ_id"/>
                <field name="lot_id"/>
                <field name="lot_price" widget="monetary"/>
                <field name="quantity" sum="Total Quantity"/>
                <field name="product_weight"/>
                <field name="total_weight" sum="Total Weight"/>
                <field name="product_rate" widget="monetary"/>
                <field name="price_source"/>
                <field name="total_value" widget="monetary" sum="Total Value"/>
                <field name="current_product_cost" widget="monetary"/>
                <field name="forecasted_qty" string="Location Qty"/>
                <field name="warehouse_qty" string="Warehouse Qty"/>
                <field name="state"/>
                <button name="action_open_picking" type="object" icon="fa-external-link" title="Open Transfer"/>
            </list>
        </field>
    </record>

    <!-- Stock Move History Report Search View -->
    <record id="view_stock_move_history_report_search" model="ir.ui.view">
        <field name="name">stock.move.history.report.search</field>
        <field name="model">stock.move.history.report</field>
        <field name="arch" type="xml">
            <search string="Stock Move History">
                <field name="reference" string="Reference"/>
                <field name="product_id" string="Product"/>
                <field name="lot_id" string="Lot/Serial No"/>
                <field name="warehouse_id" string="Warehouse"/>
                <field name="location_id" string="From Location"/>
                <field name="location_dest_id" string="To Location"/>
                <field name="product_categ_id" string="Product Category"/>
                <field name="picking_id" string="Transfer"/>

                <filter string="Receipts" name="incoming" domain="[('picking_type_code', '=', 'incoming')]"/>
                <filter string="Deliveries" name="outgoing" domain="[('picking_type_code', '=', 'outgoing')]"/>
                <filter string="Internal Transfers" name="internal" domain="[('picking_type_code', '=', 'internal')]"/>

                <filter string="With Lot/Serial" name="with_lot" domain="[('lot_id', '!=', False)]"/>
                <filter string="Without Lot/Serial" name="without_lot" domain="[('lot_id', '=', False)]"/>

                <filter string="Zero Lot Price" name="zero_lot_price" domain="[('lot_price', '=', 0)]"/>
                <filter string="Non-Zero Lot Price" name="non_zero_lot_price" domain="[('lot_price', '>', 0)]"/>

                <separator/>
                <filter string="Price from Move Line" name="price_from_move_line" domain="[('price_source', '=', 'move_line')]"/>
                <filter string="Price from Lot" name="price_from_lot" domain="[('price_source', '=', 'lot')]"/>
                <filter string="Price from Product" name="price_from_product" domain="[('price_source', '=', 'product')]"/>
                <filter string="No Price Available" name="no_price" domain="[('price_source', '=', 'none')]"/>

                <filter string="Today" name="today" domain="[('date', '&gt;=', datetime.datetime.combine(context_today(), datetime.time(0,0,0))), ('date', '&lt;=', datetime.datetime.combine(context_today(), datetime.time(23,59,59)))]"/>
                <filter string="Last 7 Days" name="last_7_days" domain="[('date', '&gt;=', (context_today() - datetime.timedelta(days=7)).strftime('%Y-%m-%d'))]"/>
                <filter string="Last 30 Days" name="last_30_days" domain="[('date', '&gt;=', (context_today() - datetime.timedelta(days=30)).strftime('%Y-%m-%d'))]"/>
                <filter string="This Month" name="this_month" domain="[('date', '&gt;=', (context_today().replace(day=1)).strftime('%Y-%m-%d'))]"/>
                <filter string="This Year" name="this_year" domain="[('date', '&gt;=', (context_today().replace(month=1, day=1)).strftime('%Y-%m-%d'))]"/>

                <separator/>
                <filter string="Done" name="done" domain="[('state', '=', 'done')]"/>

                <group expand="0" string="Group By">
                    <filter string="Product" name="groupby_product" context="{'group_by': 'product_id'}"/>
                    <filter string="Product Category" name="groupby_categ" context="{'group_by': 'product_categ_id'}"/>
                    <filter string="Lot/Serial No" name="groupby_lot" context="{'group_by': 'lot_id'}"/>
                    <filter string="Price Source" name="groupby_price_source" context="{'group_by': 'price_source'}"/>
                    <filter string="Warehouse" name="groupby_warehouse" context="{'group_by': 'warehouse_id'}"/>
                    <filter string="From Location" name="groupby_location" context="{'group_by': 'location_id'}"/>
                    <filter string="To Location" name="groupby_location_dest" context="{'group_by': 'location_dest_id'}"/>
                    <filter string="Operation Type" name="groupby_picking_type" context="{'group_by': 'picking_type_code'}"/>
                    <filter string="Status" name="groupby_state" context="{'group_by': 'state'}"/>
                    <filter string="Date" name="groupby_date" context="{'group_by': 'date:day'}"/>
                    <filter string="Date (Week)" name="groupby_date_week" context="{'group_by': 'date:week'}"/>
                    <filter string="Date (Month)" name="groupby_date_month" context="{'group_by': 'date:month'}"/>
                    <filter string="Date (Quarter)" name="groupby_date_quarter" context="{'group_by': 'date:quarter'}"/>
                    <filter string="Date (Year)" name="groupby_date_year" context="{'group_by': 'date:year'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Stock Move History Report Graph View -->
    <record id="view_stock_move_history_report_graph" model="ir.ui.view">
        <field name="name">stock.move.history.report.graph</field>
        <field name="model">stock.move.history.report</field>
        <field name="arch" type="xml">
            <graph string="Stock Move History" type="bar" sample="1">
                <field name="product_id"/>
                <field name="total_weight" type="measure"/>
                <field name="total_value" type="measure"/>
            </graph>
        </field>
    </record>

    <!-- Stock Move History Report Pivot View -->
    <record id="view_stock_move_history_report_pivot" model="ir.ui.view">
        <field name="name">stock.move.history.report.pivot</field>
        <field name="model">stock.move.history.report</field>
        <field name="arch" type="xml">
            <pivot string="Stock Move History" sample="1">
                <field name="product_id" type="row"/>
                <field name="warehouse_id" type="col"/>
                <field name="price_source" type="col"/>
                <field name="quantity" type="measure"/>
                <field name="total_weight" type="measure"/>
                <field name="total_value" type="measure"/>
                <field name="product_rate" type="measure"/>
                <field name="current_product_cost" type="measure"/>
            </pivot>
        </field>
    </record>

    <!-- Stock Move History Report Form View -->
    <record id="view_stock_move_history_report_form" model="ir.ui.view">
        <field name="name">stock.move.history.report.form</field>
        <field name="model">stock.move.history.report</field>
        <field name="arch" type="xml">
            <form string="Stock Move History">
                <header>
                    <button name="populate_report" string="Populate Report" type="object" class="oe_highlight"/>
                </header>
                <sheet>
                    <group>
                        <group>
                            <field name="reference"/>
                            <field name="date" string="Scheduled Date"/>
                            <field name="scheduled_date" string="Original Scheduled Date"/>
                            <field name="picking_id"/>
                            <field name="picking_type_code"/>
                        </group>
                        <group>
                            <field name="product_id"/>
                            <field name="product_categ_id"/>
                            <field name="lot_id"/>
                            <field name="state"/>
                        </group>
                    </group>
                    <group>
                        <group>
                            <field name="location_id"/>
                            <field name="location_dest_id"/>
                            <field name="warehouse_id"/>
                        </group>
                        <group>
                            <field name="quantity"/>
                            <field name="product_weight"/>
                            <field name="total_weight"/>
                            <field name="forecasted_qty" string="Location Qty"/>
                            <field name="warehouse_qty" string="Warehouse Qty"/>
                        </group>
                    </group>
                    <group>
                        <group>
                            <field name="lot_price"/>
                            <field name="product_rate"/>
                            <field name="total_value"/>
                            <field name="current_product_cost"/>
                        </group>
                        <group>
                            <field name="price_source"/>
                            <field name="company_id"/>
                        </group>
                    </group>
                </sheet>
            </form>
        </field>
    </record>

    <!-- Stock Move History Report Action -->
    <record id="action_stock_move_history_report" model="ir.actions.act_window">
        <field name="name">Stock Move History</field>
        <field name="res_model">stock.move.history.report</field>
        <field name="view_mode">list,form,pivot,graph</field>
        <field name="search_view_id" ref="view_stock_move_history_report_search"/>
        <field name="context">{
            'search_default_done': 1,
            'search_default_last_30_days': 1,
        }</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No stock moves found
            </p>
            <p>
                This report shows all stock moves with detailed information about products, locations, lots, and values.
            </p>
            <p>
                Click the "Populate Report" button to generate the report data.
            </p>
        </field>
    </record>

    <!-- Populate Report Action -->
    <record id="action_populate_stock_move_history_report" model="ir.actions.server">
        <field name="name">Populate Report</field>
        <field name="model_id" ref="model_stock_move_history_report"/>
        <field name="binding_model_id" ref="model_stock_move_history_report"/>
        <field name="binding_view_types">list,form</field>
        <field name="state">code</field>
        <field name="code">
            action = model.populate_report()
        </field>
    </record>

    <!-- Menu Items -->
    <menuitem id="menu_stock_move_history_report_main"
        name="Stock Move History"
        parent="stock.menu_warehouse_report"
        sequence="10"/>

    <menuitem id="menu_stock_move_history_report"
        name="View Report"
        parent="menu_stock_move_history_report_main"
        action="action_stock_move_history_report"
        sequence="10"/>

    <menuitem id="menu_populate_stock_move_history_report"
        name="Populate Report"
        parent="menu_stock_move_history_report_main"
        action="action_populate_stock_move_history_report"
        sequence="20"/>
</odoo>
