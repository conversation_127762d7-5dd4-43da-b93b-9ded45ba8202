<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <!-- Form view for product selection wizard -->
    <record id="view_product_selection_wizard_form" model="ir.ui.view">
        <field name="name">product.selection.wizard.form</field>
        <field name="model">product.selection.wizard</field>
        <field name="arch" type="xml">
            <form string="Select Product">
                <sheet>
                    <group>
                        <field name="barcode_scan" readonly="1"/>
                        <field name="available_product_ids" invisible="1"/>
                        <field name="model_name" invisible="1"/>
                        <field name="record_id" invisible="1"/>
                        <field name="product_id" options="{'no_create': True}" required="1"/>
                    </group>
                </sheet>
                <footer>
                    <button name="action_confirm" string="Confirm" type="object" class="btn-primary"/>
                    <button string="Cancel" class="btn-secondary" special="cancel"/>
                </footer>
            </form>
        </field>
    </record>

    <!-- Action for product selection wizard -->
    <record id="action_product_selection_wizard" model="ir.actions.act_window">
        <field name="name">Select Product</field>
        <field name="res_model">product.selection.wizard</field>
        <field name="view_mode">form</field>
        <field name="target">new</field>
    </record>
</odoo>
