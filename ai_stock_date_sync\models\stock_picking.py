from odoo import models, fields, api, _
from odoo.exceptions import UserError
import logging

_logger = logging.getLogger(__name__)

class StockPicking(models.Model):
    _inherit = 'stock.picking'
    
    def action_sync_effective_date(self):
        """Synchronize the effective date (date_done) with the scheduled date."""
        # Check if user has permission
        if not self.env.user.has_group('ai_stock_date_sync.group_stock_date_sync'):
            raise UserError(_("You don't have permission to synchronize stock picking dates."))
        
        # Filter pickings that are done and have a scheduled date
        valid_pickings = self.filtered(lambda p: p.state == 'done' and p.scheduled_date)
        
        if not valid_pickings:
            raise UserError(_("No valid pickings found. Only confirmed pickings with a scheduled date can be synchronized."))
        
        # Store original values for later processing
        pickings_with_date_changes = {}
        for picking in valid_pickings:
            if picking.date_done != picking.scheduled_date:
                pickings_with_date_changes[picking.id] = {
                    'old_date': picking.date_done,
                    'new_date': picking.scheduled_date
                }
        
        if not pickings_with_date_changes:
            raise UserError(_("All selected pickings already have their effective date synchronized with scheduled date."))
        
        # Update the date_done field
        for picking_id, date_info in pickings_with_date_changes.items():
            picking = self.browse(picking_id)
            picking.write({'date_done': date_info['new_date']})
            _logger.info(f"Synchronized effective date for picking {picking.name} from {date_info['old_date']} to {date_info['new_date']}")
        
        # Update related records to maintain data consistency
        # We reuse the method from ai_admin_date_edit module
        for picking in self.browse(list(pickings_with_date_changes.keys())):
            if hasattr(picking, '_update_related_records_after_date_change'):
                picking._update_related_records_after_date_change(pickings_with_date_changes)
            else:
                _logger.warning(f"Method _update_related_records_after_date_change not found. Related records may not be updated correctly.")
        
        return {
            'type': 'ir.actions.client',
            'tag': 'display_notification',
            'params': {
                'title': _('Success'),
                'message': _('%s pickings have been synchronized.') % len(pickings_with_date_changes),
                'sticky': False,
                'type': 'success',
            }
        }
    
    @api.model
    def cron_sync_effective_date(self, days_ago=7):
        """Scheduled action to synchronize effective dates with scheduled dates.
        
        Args:
            days_ago: Only process pickings done within the last X days
        """
        # Find pickings that are done, have a scheduled date, and were done within the specified period
        domain = [
            ('state', '=', 'done'),
            ('scheduled_date', '!=', False),
            ('date_done', '!=', False),
            ('date_done', '!=', 'scheduled_date'),
            ('date_done', '>=', fields.Datetime.now() - fields.Datetime.to_timedelta(days=days_ago))
        ]
        
        pickings = self.search(domain)
        if pickings:
            try:
                pickings.action_sync_effective_date()
                _logger.info(f"Cron job successfully synchronized {len(pickings)} pickings")
            except Exception as e:
                _logger.error(f"Error in cron job to synchronize effective dates: {str(e)}")
        else:
            _logger.info("No pickings found to synchronize in cron job")
