<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Extend the stock.picking form view -->
    <record id="view_picking_form_inherit_admin_date_edit" model="ir.ui.view">
        <field name="name">stock.picking.form.inherit.admin.date.edit</field>
        <field name="model">stock.picking</field>
        <field name="inherit_id" ref="stock.view_picking_form"/>
        <field name="arch" type="xml">
            <!-- Add a button to edit dates -->
            <xpath expr="//header" position="inside">
                <button name="action_edit_dates"
                        string="Edit Dates"
                        type="object"
                        groups="ai_admin_date_edit.group_admin_date_edit"
                        disabled="(not can_edit_dates)"
                        class="btn-primary"/>
            </xpath>

            <!-- Make date fields editable for admin users -->
            <xpath expr="//field[@name='scheduled_date']" position="attributes">
                <attribute name="readonly">0</attribute>
                <attribute name="disabled">(not can_edit_dates)</attribute>
            </xpath>

            <xpath expr="//field[@name='date_done']" position="attributes">
                <attribute name="readonly">0</attribute>
                <attribute name="disabled">(not can_edit_dates)</attribute>
            </xpath>

            <!-- Add the can_edit_dates field (invisible) -->
            <xpath expr="//field[@name='company_id']" position="after">
                <field name="can_edit_dates" invisible="1"/>
            </xpath>
        </field>
    </record>
</odoo>
