<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <record id="view_manufacturing_cost_report_tree" model="ir.ui.view">
        <field name="name">manufacturing.cost.report.tree</field>
        <field name="model">manufacturing.cost.report</field>
        <field name="arch" type="xml">
            <list string="Manufacturing Cost Report" create="false" edit="false">
                <field name="production_id"/>
                <field name="product_id"/>
                <field name="hamali_cost" sum="Total Hamali"/>
                <field name="sortex_landed_cost" sum="Total Sortex"/>
                <field name="ci_landed_cost" sum="Total CI"/>
                <field name="total_bag_cost" sum="Total Bag Cost"/>
                <field name="profit_loss" sum="Total Profit/Loss"/>
                <field name="date"/>
                <field name="warehouse_id"/>
            </list>
        </field>
    </record>

    <!-- Search View -->
    <record id="view_manufacturing_cost_report_search" model="ir.ui.view">
        <field name="name">manufacturing.cost.report.search</field>
        <field name="model">manufacturing.cost.report</field>
        <field name="arch" type="xml">
            <search string="Manufacturing Cost Report">
                <field name="production_id"/>
                <field name="product_id"/>
                <field name="warehouse_id"/>
                <filter string="This Month" name="this_month" domain="[('date','&gt;=', (context_today() + relativedelta(day=1)).strftime('%Y-%m-%d')), ('date','&lt;=', (context_today() + relativedelta(months=1, day=1, days=-1)).strftime('%Y-%m-%d'))]"/>
                <group expand="1" string="Group By">
                    <filter string="Product" name="product" context="{'group_by':'product_id'}"/>
                    <filter string="Warehouse" name="warehouse" context="{'group_by':'warehouse_id'}"/>
                    <filter string="Month" name="month" context="{'group_by':'date:month'}"/>
                </group>
            </search>
        </field>
    </record>

    <!-- Action -->
    <record id="action_manufacturing_cost_report" model="ir.actions.act_window">
        <field name="name">Manufacturing Cost Report</field>
        <field name="res_model">manufacturing.cost.report</field>
        <field name="view_mode">tree</field>
        <field name="search_view_id" ref="view_manufacturing_cost_report_search"/>
        <field name="context">{'search_default_this_month': 1}</field>
        <field name="help" type="html">
            <p class="o_view_nocontent_smiling_face">
                No data available.
            </p>
        </field>
    </record>


    <!-- List View -->
    <record id="action_manufacturing_cost_report" model="ir.actions.act_window">
        <field name="name">Manufacturing Cost Details</field>
        <field name="res_model">manufacturing.cost.report</field>
        <field name="view_mode">list</field>
        <field name="view_id" ref="view_manufacturing_cost_report_tree"/>
        
    </record>
    <menuitem 
        id="menu_manufacturing_cost_report" 
        name="Manufacturing Cost Details" 
        parent="account.menu_finance_reports" 
        action="action_manufacturing_cost_report" 
        sequence="10"
    />
</odoo>