from odoo import models, api
from datetime import date
import logging

_logger = logging.getLogger(__name__)

class StockPicking(models.Model):
    _inherit = 'stock.picking'

    def _create_sales_agent_commission(self):
        """Create sales agent commission record when delivery is done"""
        self.ensure_one()
        if (self.sale_id and 
            self.sale_id.x_commission_fees and 
            self.sale_id.x_sales_agent_id and 
            self.state == 'done' and 
            self.picking_type_code == 'outgoing'):
            
            # Check if commission record already exists
            existing_commission = self.env['sales.agent.commission'].search([
                ('sale_order_id', '=', self.sale_id.id)
            ])
            
            if not existing_commission:
                self.env['sales.agent.commission'].create({
                    'sale_order_id': self.sale_id.id,
                    'sales_agent_id': self.sale_id.x_sales_agent_id.id,
                    'sale_date': self.sale_id.date_order,
                    'delivery_date': date.today(),
                })

    def button_validate(self):
        """Override to create commission record after delivery validation"""
        res = super(StockPicking, self).button_validate()
        if res is True:
            self._create_sales_agent_commission()
        return res
