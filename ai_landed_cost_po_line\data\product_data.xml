<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <data noupdate="1">
        <record id="product_category_landed_costs" model="product.category">
            <field name="name">Landed Costs</field>
            <field name="parent_id" ref="product.product_category_all"/>
        </record>

        <record id="product_landed_cost" model="product.template">
            <field name="name">Landed Costs</field>
            <field name="detailed_type">service</field>
            <field name="categ_id" ref="product_category_landed_costs"/>
            <field name="landed_cost_ok" eval="1"/>
            <field name="purchase_ok" eval="1"/>
            <field name="sale_ok" eval="0"/>
        </record>
    </data>
</odoo>
