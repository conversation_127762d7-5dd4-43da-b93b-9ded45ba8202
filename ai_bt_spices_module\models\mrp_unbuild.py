from odoo import models, api, _
import logging

_logger = logging.getLogger(__name__)

class MrpUnbuild(models.Model):
    _inherit = 'mrp.unbuild'

    def action_unbuild(self):
        """Override the action_unbuild method to reset cost analysis fields in the MO."""
        # Call the original method to perform the unbuild operation
        result = super(MrpUnbuild, self).action_unbuild()
        
        # Reset cost analysis fields in the manufacturing order
        if self.mo_id:
            _logger.info("Unbuild operation completed for MO %s. Resetting cost analysis fields.", self.mo_id.name)
            try:
                self.mo_id.reset_cost_analysis_fields()
                _logger.info("Cost analysis fields reset successfully for MO %s", self.mo_id.name)
            except Exception as e:
                _logger.error("Error resetting cost analysis fields for MO %s: %s", self.mo_id.name, str(e), exc_info=True)
        
        return result
