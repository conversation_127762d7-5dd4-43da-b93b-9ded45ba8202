from odoo import models, fields, api, _
from odoo.exceptions import UserError
import logging
from datetime import datetime

_logger = logging.getLogger(__name__)

class StockPicking(models.Model):
    _inherit = 'stock.picking'

    can_edit_dates = fields.Boolean(
        compute='_compute_can_edit_dates',
        string='Can Edit Dates',
        help='Technical field to determine if the user can edit dates on confirmed pickings'
    )

    @api.depends('state')
    def _compute_can_edit_dates(self):
        """Determine if the user can edit dates on this picking."""
        is_admin_date_editor = self.env.user.has_group('ai_admin_date_edit.group_admin_date_edit')
        _logger.info(f"User {self.env.user.name} (ID: {self.env.user.id}) has admin date edit group: {is_admin_date_editor}")

        for picking in self:
            # Always enable editing for testing
            picking.can_edit_dates = True
            _logger.info(f"FORCED Picking {picking.name} (ID: {picking.id}) state: {picking.state}, can_edit_dates: True")

    def action_edit_dates(self):
        """Open a wizard to edit dates on the picking."""
        self.ensure_one()
        if not self.can_edit_dates:
            raise UserError(_("You don't have permission to edit dates on this picking."))

        return {
            'name': _('Edit Dates'),
            'type': 'ir.actions.act_window',
            'view_mode': 'form',
            'res_model': 'stock.picking',
            'res_id': self.id,
            'target': 'new',
            'context': {
                'edit_dates': True,
            },
        }

    def write(self, vals):
        """Override write to allow changing dates on confirmed pickings."""
        # Check if we're editing dates and if the user has permission
        is_admin_date_editor = self.env.user.has_group('ai_admin_date_edit.group_admin_date_edit')
        is_editing_dates = any(field in vals for field in ['scheduled_date', 'date_done'])

        # Store original values for later processing
        pickings_with_date_done_change = {}
        if 'date_done' in vals:
            for picking in self:
                if picking.state == 'done' and picking.date_done and picking.date_done != vals['date_done']:
                    pickings_with_date_done_change[picking.id] = {
                        'old_date': picking.date_done,
                        'new_date': vals['date_done']
                    }

        # If editing dates on confirmed pickings, ensure the user has permission
        if is_editing_dates and any(picking.state not in ['draft', 'cancel'] for picking in self):
            if not is_admin_date_editor:
                raise UserError(_("You don't have permission to edit dates on confirmed pickings."))

            # Log the date changes for audit purposes
            for picking in self:
                if picking.state not in ['draft', 'cancel']:
                    changes = []
                    if 'scheduled_date' in vals and picking.scheduled_date != vals['scheduled_date']:
                        changes.append(f"scheduled_date: {picking.scheduled_date} -> {vals['scheduled_date']}")
                    if 'date_done' in vals and picking.date_done != vals['date_done']:
                        changes.append(f"date_done: {picking.date_done} -> {vals['date_done']}")

                    if changes:
                        _logger.info(f"Admin {self.env.user.name} changed dates on picking {picking.name}: {', '.join(changes)}")

        # Proceed with the write operation
        result = super(StockPicking, self).write(vals)

        # Update related records if date_done was changed
        if pickings_with_date_done_change and is_admin_date_editor:
            self._update_related_records_after_date_change(pickings_with_date_done_change)

        return result

    def _update_related_records_after_date_change(self, pickings_with_date_change):
        """Update related records when date_done is changed on a done picking."""
        for picking_id, date_info in pickings_with_date_change.items():
            picking = self.browse(picking_id)
            old_date = date_info['old_date']
            new_date = date_info['new_date']

            _logger.info(f"Updating related records for picking {picking.name} after date_done change from {old_date} to {new_date}")

            # 1. Update stock move dates
            moves = picking.move_ids.filtered(lambda m: m.state == 'done')
            if moves:
                moves.write({'date': new_date})
                _logger.info(f"Updated date on {len(moves)} stock moves to {new_date}")

            # 2. Update stock move line dates
            move_lines = picking.move_line_ids.filtered(lambda ml: ml.state == 'done')
            if move_lines:
                move_lines.write({'date': new_date})
                _logger.info(f"Updated date on {len(move_lines)} stock move lines to {new_date}")

            # 3. Update stock valuation layers
            valuation_layers_total = 0
            for move in moves:
                valuation_layers = self.env['stock.valuation.layer'].search([
                    ('stock_move_id', '=', move.id)
                ])
                if valuation_layers:
                    # Update the create_date and accounting_date
                    valuation_layers.write({
                        'create_date': new_date,
                        'accounting_date': new_date,
                    })
                    valuation_layers_total += len(valuation_layers)

            if valuation_layers_total > 0:
                _logger.info(f"Updated dates on {valuation_layers_total} stock valuation layers to {new_date}")

            # 4. Update accounting entries related to this picking
            # First, direct account moves linked to the picking
            account_moves = self.env['account.move'].search([
                '|',
                ('stock_move_id', 'in', moves.ids),
                ('picking_id', '=', picking.id)
            ])

            if account_moves:
                for account_move in account_moves:
                    if account_move.state != 'posted':
                        account_move.write({
                            'date': new_date,
                            'invoice_date': new_date if account_move.is_invoice() else False,
                        })
                        _logger.info(f"Updated date on account move {account_move.name} to {new_date}")
                    else:
                        _logger.warning(f"Account move {account_move.name} is already posted and cannot be updated automatically")

            # 5. Update stock quants
            quants_updated = 0
            for move in moves:
                # For incoming and internal moves, update destination quants
                if move.location_dest_id.usage in ['internal', 'transit']:
                    quants = self.env['stock.quant'].search([
                        ('product_id', '=', move.product_id.id),
                        ('location_id', '=', move.location_dest_id.id),
                        ('in_date', '=', old_date)
                    ])
                    if quants:
                        quants.write({'in_date': new_date})
                        quants_updated += len(quants)

                # For outgoing moves, update out_date if it exists
                if move.location_id.usage in ['internal', 'transit'] and hasattr(self.env['stock.quant'], 'out_date'):
                    quants = self.env['stock.quant'].search([
                        ('product_id', '=', move.product_id.id),
                        ('location_id', '=', move.location_id.id),
                        ('out_date', '=', old_date)
                    ])
                    if quants:
                        quants.write({'out_date': new_date})
                        quants_updated += len(quants)

            if quants_updated > 0:
                _logger.info(f"Updated dates on {quants_updated} stock quants to {new_date}")

            # 6. Update lot/serial numbers if this is a receipt
            if picking.picking_type_code == 'incoming':
                lots_updated = 0
                for move_line in move_lines:
                    if move_line.lot_id and move_line.lot_id.create_date == old_date:
                        move_line.lot_id.write({'create_date': new_date})
                        lots_updated += 1

                if lots_updated > 0:
                    _logger.info(f"Updated create_date on {lots_updated} lot/serial numbers to {new_date}")

            # 7. Update landed costs if they exist and are linked to this picking
            landed_costs = self.env['stock.landed.cost'].search([
                ('picking_ids', 'in', picking.id)
            ])

            if landed_costs:
                for landed_cost in landed_costs:
                    if landed_cost.state != 'done':
                        landed_cost.write({'date': new_date})
                        _logger.info(f"Updated date on landed cost {landed_cost.name} to {new_date}")
                    else:
                        _logger.warning(f"Landed cost {landed_cost.name} is already validated and cannot be updated automatically")

            # 8. Update inventory adjustments if they are linked to this picking
            inventory_adjustments = self.env['stock.inventory'].search([
                ('move_ids', 'in', moves.ids)
            ])

            if inventory_adjustments:
                for inventory in inventory_adjustments:
                    inventory.write({'date': new_date})
                    _logger.info(f"Updated date on inventory adjustment {inventory.name} to {new_date}")

            _logger.info(f"Completed updating related records for picking {picking.name}")

            # Add a note to the picking for audit purposes
            picking.message_post(
                body=_(f"Date done changed from {old_date} to {new_date} by {self.env.user.name}. "
                      f"Related records have been updated accordingly."),
                subtype_id=self.env.ref('mail.mt_note').id
            )
