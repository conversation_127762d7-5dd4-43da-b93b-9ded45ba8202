<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!--
        Note on view inheritance approach:
        We're adding a new group with fields to the Purchase Order form view
        after the Commission and Market Fees group.
    -->

    <!-- Extend Purchase Order Form View -->

    <record id="purchase_order_form_inherit" model="ir.ui.view">
        <field name="name">purchase.order.form.inherit.product.variant.weight</field>
        <field name="model">purchase.order</field>
        <field name="inherit_id" ref="purchase.purchase_order_form"/>
        <field name="arch" type="xml">
            <!-- Add our fields after the x_broker_id field -->
            <xpath expr="//field[@name='x_brokerage_fees']" position="after">

            <separator string="Add Bharati Details"/>
                <field name="x_reference_product_id" string="Reference Product"/>
                <field name="x_bharati_weight" string="Bharati (Weight)"/>
                <button name="action_create_product_variant"
                        type="object"
                        string="Create Product Variant and Add to Order"
                        class="oe_highlight"
                        />
                        <!-- disabled="(not x_reference_product_id) or x_bharati_weight &lt;= 0" -->
            </xpath>
        </field>
    </record>
</odoo>
