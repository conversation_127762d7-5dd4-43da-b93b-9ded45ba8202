{
    'name': 'Manufacturing Order Product Variant Weight',
    'version': '********.0',
    'category': 'Manufacturing',
    'summary': 'Extends Manufacturing Order with Reference Product Variant and Bharati (Weight) fields',
    'description': """
        This module extends Manufacturing Order with:
        - Reference Product Variant field: Select an existing product as a reference
        - <PERSON><PERSON><PERSON> (Weight) field: Specify the weight for the new product variant
        - <PERSON><PERSON> to create new products with updated weight
        - Automatically updates product name by replacing only the weight part while maintaining the naming convention
        - Example: From 'AJMO DIAMOND 500G [30KG]' to 'AJMO DIAMOND 50G [30KG]' when changing the weight
    """,
    'author': 'Arihant AI',
    'website': 'https://www.arihantai.com',
    'depends': [
        'mrp',
        'ai_bt_spices_module',
    ],
    'data': [
        'views/mrp_production_views.xml',
    ],
    'installable': True,
    'application': False,
    'auto_install': False,
    'license': 'LGPL-3',
}
