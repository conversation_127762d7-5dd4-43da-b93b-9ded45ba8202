<?xml version="1.0" encoding="utf-8"?>
<odoo>
    <!-- Inherit Sale Order Form View to modify date fields -->
    <record id="view_sale_order_form_date_fields" model="ir.ui.view">
        <field name="name">sale.order.form.date.fields</field>
        <field name="model">sale.order</field>
        <field name="inherit_id" ref="sale.view_order_form"/>
        <field name="arch" type="xml">
            <!-- Make validity_date invisible -->
            <xpath expr="//field[@name='validity_date']" position="attributes">
                <attribute name="invisible">1</attribute>
            </xpath>

            <!-- Make the first date_order label visible to all users -->
            <xpath expr="//div[contains(@class, 'o_td_label')][.//label[@for='date_order']][1]" position="attributes">
                <attribute name="groups">base.group_user</attribute>
                <attribute name="invisible">0</attribute>
            </xpath>

            <!-- Make the first date_order field visible to all users -->
            <xpath expr="//div[contains(@class, 'o_td_label')][.//label[@for='date_order']][1]/following-sibling::field[@name='date_order'][1]" position="attributes">
                <attribute name="groups">base.group_user</attribute>
                <attribute name="invisible">0</attribute>
            </xpath>

            <!-- Add create_date field after date_order -->
            <!-- <xpath expr="//div[contains(@class, 'o_td_label')][.//label[@for='date_order']][1]/following-sibling::field[@name='date_order'][1]" position="after">
                <field name="create_date" string="Sales Order Date" readonly="1"/>
            </xpath> -->
        </field>
    </record>
</odoo>
