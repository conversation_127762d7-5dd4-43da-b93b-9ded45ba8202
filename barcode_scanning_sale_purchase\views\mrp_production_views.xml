<?xml version="1.0" encoding="utf-8" ?>
<odoo>
    <!-- View for manufacturing order raw materials -->
    <record id="mrp_production_form_view_inherit" model="ir.ui.view">
        <field name="name">
            mrp.production.form.view.inherit.barcode.scanning.sale.purchase
        </field>
        <field name="model">mrp.production</field>
        <field name="inherit_id" ref="ai_bt_spices_module.mrp_production_subcontract_form_inherit"/>
        <field name="arch" type="xml">
            <!-- Add barcode scanning to raw materials -->
            <xpath expr="//field[@name='move_raw_material_ids']/list/control" position="after">
                <field name="barcode_scan" force_save="1" string="Lot No"
                       help="Enter the lot number or scan product barcode. The system will find the product while preserving the lot number value."/>
                <button name="action_open_product_wizard" string="Select" type="object"
                        icon="fa-search" invisible="not barcode_scan or parent.state not in ('draft', 'confirmed', 'progress')"
                        help="Open wizard to select product for this lot number"/>
            </xpath>

            <xpath expr="//field[@name='move_raw_material_ids']/list/field[@name='product_id']"
                   position="attributes">
                <attribute name="options">{'no_create': True}</attribute>
                <!-- Ensure no context overrides our domain -->
                <attribute name="context">{'default_is_storable': True}</attribute>
            </xpath>

            <!-- Add barcode scanning to other materials -->
            <xpath expr="//field[@name='move_other_ids']/list/control" position="after">
                <field name="barcode_scan" force_save="1" string="Lot No"
                       help="Enter the lot number or scan product barcode. The system will find the product while preserving the lot number value."/>
                <button name="action_open_product_wizard" string="Select" type="object"
                        icon="fa-search" invisible="not barcode_scan"
                        help="Open wizard to select product for this lot number"/>
            </xpath>

            <xpath expr="//field[@name='move_other_ids']/list/field[@name='product_id']"
                   position="attributes">
                <attribute name="options">{'no_create': True}</attribute>
                <!-- Ensure no context overrides our domain -->
                <attribute name="context">{'default_is_storable': True}</attribute>
            </xpath>
        </field>
    </record>
</odoo>
