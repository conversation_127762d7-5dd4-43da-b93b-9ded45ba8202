<?xml version="1.0" encoding="utf-8"?>
<odoo>
<!--    It's the pdf template for showing the turnover report-->
    <record id="inventory_turnover_report" model="ir.actions.report">
        <field name="name">Inventory Turnover Analysis Report</field>
        <field name="model">turnover.report</field>
        <field name="report_type">qweb-pdf</field>
        <field name="report_name">inventory_turnover_report_analysis.inventory_pdf_turnover_report</field>
        <field name="report_file">inventory_turnover_report_analysis.inventory_pdf_turnover_report</field>
        <field name="binding_model_id" ref="model_turnover_report"/>
        <field name="binding_type">report</field>
    </record>
    <template id="inventory_pdf_turnover_report">
        <t t-call="web.html_container">
            <t t-call="web.external_layout">
                <div class="page">
                    <center>
                        <h2>Inventory Turnover Analysis Report</h2>
                    </center>
                </div><br/>
                <table class="table">
                        <tr>
                            <td>
                                <t t-if="start_date">
                                    <b>Start date:</b>
                                    <t t-esc="start_date"/>
                                </t>
                            </td>
                            <td>
                                <t t-if="end_date">
                                    <b>End date:</b>
                                    <t t-esc="end_date"/>
                                </t>
                            </td>
                        </tr>
                    </table>
                <table class="table">
                    <thead>
                        <tr style="text-align: center;">
                            <th style="text-align: left;width:10%">Product</th>
                            <th style="width:5%">Opening Stock</th>
                            <th style="width:5%">Closing Stock</th>
                            <th style="width:5%">Average Stock</th>
                            <th style="width:5%">Sales Count</th>
                            <th style="width:5%">Purchase Count</th>
                            <th style="width:5%">Turnover Ratio</th>
                        </tr>
                    </thead>
                    <tbody>
                        <t t-foreach="stock_report" t-as="i">
                            <tr style="text-align: center;">
                                <td style="text-align: left;" t-esc="i['product']"/>
                                <td t-esc="i['opening_stock']"/>
                                <td t-esc="i['closing_stock']"/>
                                <td t-esc="i['average_stock']"/>
                                <td t-esc="i['sale_count']"/>
                                <td t-esc="i['purchase_count']"/>
                                <td t-esc="i['turnover_ratio']"/>
                            </tr>
                        </t>
                    </tbody>
                </table>
            </t>
        </t>
    </template>
</odoo>
