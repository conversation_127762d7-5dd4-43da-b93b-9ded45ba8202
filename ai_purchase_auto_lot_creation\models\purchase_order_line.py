# -*- coding: utf-8 -*-
###############################################################################
#
#    Cybrosys Technologies Pvt. Ltd.
#
#    Copyright (C) 2024-TODAY Cybrosys Technologies(<https://www.cybrosys.com>)
#    Author: Ranjith R(<EMAIL>)
#
#    You can modify it under the terms of the GNU AFFERO
#    GENERAL PUBLIC LICENSE (AGPL v3), Version 3.
#
#    This program is distributed in the hope that it will be useful,
#    but WITHOUT ANY WARRANTY; without even the implied warranty of
#    MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
#    GNU AFFERO GENERAL PUBLIC LICENSE (AGPL v3) for more details.
#
#    You should have received a copy of the GNU AFFERO GENERAL PUBLIC LICENSE
#    (AGPL v3) along with this program.
###############################################################################
from odoo import models, fields, api
from odoo.exceptions import ValidationError

class PurchaseOrderLine(models.Model):
    """
    Enhanced Purchase Order Line to support multiple lines of the same product
    with different quantities, prices, and automatic lot creation.
    """
    _inherit = "purchase.order.line"

    lot_ids = fields.One2many(
        'custom.stock.lot', 'line_id', string="Lot",
        domain="[('id', '=', 0)]",
        help="Lot name to create for this order line"
    )
    
    line_sequence = fields.Integer(
        string='Sequence',
        default=10,
        help="Unique sequence number for each line of the same product"
    )

    @api.model
    def create(self, vals):
        """Override create to handle multiple lines of the same product"""
        if vals.get('order_id') and vals.get('product_id'):
            existing_lines = self.search([
                ('order_id', '=', vals['order_id']),
                ('product_id', '=', vals['product_id'])
            ])
            if existing_lines:
                max_sequence = max(existing_lines.mapped('line_sequence'))
                vals['line_sequence'] = max_sequence + 10
        return super(PurchaseOrderLine, self).create(vals)

    def _prepare_stock_move_vals(self, picking, price_unit, product_uom_qty, product_uom):
        """Prepare values for stock move creation with line sequence"""
        self.ensure_one()
        vals = super(PurchaseOrderLine, self)._prepare_stock_move_vals(
            picking, price_unit, product_uom_qty, product_uom
        )
        vals.update({
            'sequence': self.line_sequence,
            'name': f"{vals.get('name', '')} (Line {self.line_sequence})"
        })
        return vals

    def _create_stock_moves(self, picking):
        """Enhanced method to create stock moves handling multiple lines of the same product"""
        moves = self.env['stock.move']
        for line in self:
            line_moves = super(PurchaseOrderLine, line)._create_stock_moves(picking)
            
            if line_moves:
                for move in line_moves:
                    if line.lot_ids:
                        # Calculate quantity per lot for this specific line
                        qty_per_lot = line.product_uom_qty / len(line.lot_ids)
                        
                        # Create stock move lines for each lot
                        move_lines = []
                        for lot in line.lot_ids:
                            move_line_vals = {
                                'company_id': line.env.company.id,
                                'picking_id': move.picking_id.id,
                                'product_id': move.product_id.id,
                                'location_id': move.location_id.id,
                                'location_dest_id': move.location_dest_id.id,
                                'lot_name': lot.name,
                                'quantity': qty_per_lot,
                                'description_picking': f"{move.product_id.display_name} (Line {line.line_sequence})",
                                'move_id': move.id
                            }
                            move_lines.append(move_line_vals)
                        
                        if move_lines:
                            line.env['stock.move.line'].create(move_lines)
                
                moves |= line_moves
        
        return moves

    @api.constrains('product_id', 'product_uom_qty', 'price_unit')
    def _check_line_validity(self):
        """Ensure valid quantity and price for each line"""
        for line in self:
            if line.product_uom_qty <= 0:
                raise ValidationError("Quantity must be greater than zero.")
            if line.price_unit <= 0:
                raise ValidationError("Price must be greater than zero.")