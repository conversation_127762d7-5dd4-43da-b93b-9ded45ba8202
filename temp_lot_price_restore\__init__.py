from . import models
from . import wizards

from datetime import datetime, timedelta

def _create_cron_job(env):
    """
    Create the scheduled action for lot price restoration
    This is done in Python code to avoid XML parsing issues in Odoo 17.0

    In Odoo 17.0, post_init_hook functions receive the environment directly,
    not the cursor and registry as in previous versions.
    """
    # Check if the cron job already exists
    existing_cron = env['ir.cron'].search([
        ('name', '=', 'Restore Lot Prices - Daily'),
        ('model_id.model', '=', 'restore.lot.price.wizard')
    ])

    if not existing_cron:
        # Get the model ID for restore.lot.price.wizard
        model_id = env['ir.model'].search([('model', '=', 'restore.lot.price.wizard')], limit=1).id

        # Create a minimal cron job with only essential fields for Odoo 17.0
        vals = {
            'name': 'Restore Lot Prices - Daily',
            'model_id': model_id,
            'active': False,
        }

        # Set fields for Odoo 18.0
        try:
            vals.update({
                'state': 'code',
                'code': 'model.run_scheduled_restoration()',
                'user_id': env.ref('base.user_root').id,
                'interval_number': 1,
                'interval_type': 'days',
                'nextcall': (datetime.now() + timedelta(days=1)).strftime('%Y-%m-%d %H:%M:%S'),
            })
        except Exception as e:
            # Log the error but continue with minimal fields
            print(f"Warning: Could not set all cron job fields: {e}")

        env['ir.cron'].create(vals)
