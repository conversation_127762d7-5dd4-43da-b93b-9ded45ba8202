# -*- coding: utf-8 -*-
{
    'name': 'Temporary Lot Price Restore',
    'version': '1.0',
    'category': 'Inventory',
    'summary': 'Restore lot prices and view stock move history',
    'description': """
        This module provides:
        1. A tool to restore lot prices from stock valuation data.
           It analyzes stock valuation layers to identify the correct lot prices and
           updates the lot records accordingly.
        2. A comprehensive Stock Move History report with detailed information about
           products, locations, lots, and values.
    """,
    'author': 'Arihant AI',
    'depends': [
        'stock',
        'product',
        'ai_bt_spices_module',
        'ai_prodcut_rate_import',
    ],
    'data': [
        'security/ir.model.access.csv',
        'wizards/restore_lot_price_views.xml',
        'views/stock_move_history_report_views.xml',
        'data/ir_cron_stock_move_history.xml',
        # 'data/ir_cron_data.xml',  # Cron job is now created in Python code
    ],
    'post_init_hook': '_create_cron_job',
    'installable': True,
    'application': False,
    'license': 'LGPL-3',
}
