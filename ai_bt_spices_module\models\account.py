from odoo import models, fields, api, _
from odoo.exceptions import UserError
import logging
from datetime import datetime

_logger = logging.getLogger(__name__)

class VendorBill(models.Model):
    _inherit = 'account.move'

    # Custom fields to hold default values
    x_invoice_date = fields.Datetime(string='Invoice Date', default=fields.Datetime.now, required=True)
    x_date = fields.Datetime(string='Date', default=fields.Datetime.now, required=True)

    @api.model
    def _get_default_invoice_date(self):
        default_date = self.env['ir.config_parameter'].sudo().get_param('vendor_bill.default_invoice_date')
        if default_date:
            try:
                return datetime.strptime(default_date, '%Y-%m-%d %H:%M:%S')
            except (ValueError, TypeError):
                return fields.Datetime.now()
        return fields.Datetime.now()

    @api.model
    def _get_default_date(self):
        default_date = self.env['ir.config_parameter'].sudo().get_param('vendor_bill.default_date')
        if default_date:
            try:
                return datetime.strptime(default_date, '%Y-%m-%d %H:%M:%S')
            except (ValueError, TypeError):
                return fields.Datetime.now()
        return fields.Datetime.now()

    @api.model
    def create(self, vals):
        # Call the super to create the vendor bill
        bill = super(VendorBill, self).create(vals)

        # Update parameters with the newly created values
        self.env['ir.config_parameter'].sudo().set_param('vendor_bill.default_invoice_date', bill.x_invoice_date.strftime('%Y-%m-%d %H:%M:%S'))
        self.env['ir.config_parameter'].sudo().set_param('vendor_bill.default_date', bill.x_date.strftime('%Y-%m-%d %H:%M:%S'))

        return bill

    def write(self, vals):
        res = super(VendorBill, self).write(vals)

        # Update parameters if the custom fields are modified
        if 'x_invoice_date' in vals and vals['x_invoice_date'] != self.x_invoice_date:
            self.env['ir.config_parameter'].sudo().set_param('vendor_bill.default_invoice_date', vals['x_invoice_date'].strftime('%Y-%m-%d %H:%M:%S'))

        if 'x_date' in vals and vals['x_date'] != self.x_date:
            self.env['ir.config_parameter'].sudo().set_param('vendor_bill.default_date', vals['x_date'].strftime('%Y-%m-%d %H:%M:%S'))

        return res
        
        return super(VendorBill, self).write(vals)

class AccountMove(models.Model):
    _inherit = 'account.move'

    total_cash_payment = fields.Float(string="Total Cash Payment", store=True)
    total_bank_payment = fields.Float(string="Total Bank Payment", store=True)

    total_pending_cash = fields.Float(string="Total Pending Cash Amount", store=True, compute='_compute_pending_cash')
    total_pending_bank = fields.Float(string="Total Pending Bank Amount", store=True, compute='_compute_pending_bank')

    total_cash_paid = fields.Float(string="Total Cash Paid", default=0.0, store=True, tracking=True)
    total_bank_paid = fields.Float(string="Total Bank Paid", default=0.0, store=True, tracking=True)

    def _update_payment_status(self):
        """
        Update payment status based on payment type and amount.
        
        :param payment_type: 'cash' or 'bank'
        :param amount: amount of payment
        """
        self.ensure_one()
        
        try:
            if payment_type == 'cash':
                # Update total cash paid
                self.total_cash_paid += amount
            elif payment_type == 'bank':
                # Update total bank paid
                self.total_bank_paid += amount
            else:
                raise ValueError(f"Invalid payment type: {payment_type}")
            
            # Log the payment update with more context
            _logger.info(f"Payment update for move {self.name}: Type - {payment_type}, Amount - {amount}, "
                         f"Total Cash Paid - {self.total_cash_paid}, Total Bank Paid - {self.total_bank_paid}")
            
            # Check if fully paid and update payment state
            if float_compare(self.total_cash_paid, self.total_cash_payment, precision_digits=2) >= 0 and \
               float_compare(self.total_bank_paid, self.total_bank_payment, precision_digits=2) >= 0:
                self.payment_state = 'paid'
        
        except Exception as e:
            _logger.error(f"Error updating payment status: {str(e)}")
            raise

    def action_register_payment(self):
        """
        Override to track payment details after payment registration.
        """
        # Call the parent method first
        action = super(AccountMove, self).action_register_payment()
        
        # Iterate through payments made
        for payment in self.payment_ids:
            if payment.journal_id.type == 'cash':
                self._update_payment_status('cash', payment.amount)
            elif payment.journal_id.type == 'bank':
                self._update_payment_status('bank', payment.amount)
        
        return action

    @api.depends('amount_residual', 'payment_state', 'move_type', 'total_cash_paid', 'total_bank_paid', 'total_cash_payment', 'total_bank_payment')
    def _compute_pending_cash(self):
        for move in self:
            # Compute total payments if not already done
            if not hasattr(move, 'total_cash_paid'):
                move._compute_total_payments()
            
            # Handle both vendor bills and customer invoices
            if move.move_type in ['in_invoice', 'out_invoice']:
                # Calculate pending cash amount using total_cash_payment instead of amount_total
                pending_cash = max(0, move.total_cash_payment - move.total_cash_paid)
                move.total_pending_cash = pending_cash
                
                # Log detailed information for tracking
                _logger.info(f"Pending Cash Calculation - Invoice: {move.name}, "
                             f"Total Cash Amount: {move.total_cash_payment}, "
                             f"Total Cash Paid: {move.total_cash_paid}, "
                             f"Pending Cash: {move.total_pending_cash}")
            else:
                move.total_pending_cash = 0.0

    @api.depends('amount_residual', 'payment_state', 'move_type', 'total_cash_paid', 'total_bank_paid', 'total_cash_payment', 'total_bank_payment')
    def _compute_pending_bank(self):
        for move in self:
            # Compute total payments if not already done
            # if not hasattr(move, 'total_bank_paid'):
            move._compute_total_payments()
            
            # Handle both vendor bills and customer invoices
            if move.move_type in ['in_invoice', 'out_invoice']:
                # Calculate pending bank amount using total_bank_payment instead of amount_total
                pending_bank = max(0, move.total_bank_payment - move.total_bank_paid)
                move.total_pending_bank = pending_bank
                
                # Log detailed information for tracking
                _logger.info(f"Pending Bank Calculation - Invoice: {move.name}, "
                             f"Total Bank Amount: {move.total_bank_payment}, "
                             f"Total Bank Paid: {move.total_bank_paid}, "
                             f"Pending Bank: {move.total_pending_bank}")
            else:
                move.total_pending_bank = 0.0

    # Ensure these fields are stored and can be computed
    total_cash_paid = fields.Float(
        string="Total Cash Paid", 
        compute='_compute_total_payments', 
        store=True, 
        tracking=True
    )
    total_bank_paid = fields.Float(
        string="Total Bank Paid", 
        compute='_compute_total_payments', 
        store=True, 
        tracking=True
    )

    def _compute_total_payments(self):
        """
        Compute total cash and bank payments for the invoice
        """
        for move in self:
            # Find all payments for this move
            payments = self.env['account.payment'].search([
                ('invoice_ids', 'in', move.id)
            ])
            
            # Calculate total cash and bank payments
            cash_payments = payments.filtered(lambda p: p.journal_id.type == 'cash')
            bank_payments = payments.filtered(lambda p: p.journal_id.type == 'bank')
            
            move.total_cash_paid = sum(cash_payments.mapped('amount'))
            move.total_bank_paid = sum(bank_payments.mapped('amount'))
            
            _logger.info(f"Payment Calculation - Invoice: {move.name}, "
                         f"Total Cash Paid: {move.total_cash_paid}, "
                         f"Total Bank Paid: {move.total_bank_paid}")

    @api.model
    def create(self, vals):
        # Check if the vendor bill is created from a purchase order
        if vals.get('invoice_origin'):
            purchase_order = self.env['purchase.order'].search([('name', '=', vals['invoice_origin'])], limit=1)
            if purchase_order:
                _logger.info("Checking this purchase Order : " + str(purchase_order.name))
                _logger.info("Checking this purchase Order Cash : " + str(purchase_order.total_cash_payment))
                _logger.info("Checking this purchase Order Bank : " + str(purchase_order.total_bank_payment))
                vals['purchase_id'] = purchase_order.id
                # self.total_bank_payment = purchase_order.total_bank_payment
                # self.total_cash_payment = purchase_order.total_cash_payment
                vals['total_cash_payment'] = purchase_order.total_cash_payment
                vals['total_bank_payment'] = purchase_order.total_bank_payment
                # self._compute_total_cash_payment(purchase_order)
                # self._compute_total_bank_payment(purchase_order)

        return super(AccountMove, self).create(vals)
    
    def _compute_total_cash_payment(self, purchase_order):
        for bill in self:
            # purchase_order = None
            # if bill.invoice_origin:
            #     purchase_order = self.env['purchase.order'].search([('name', '=', str(bill.invoice_origin))], limit=1)
            bill.total_cash_payment = purchase_order.total_cash_payment

    def _compute_total_bank_payment(self, purchase_order):
        for bill in self:
            # purchase_order = None
            # if bill.invoice_origin:
            #     purchase_order = self.env['purchase.order'].search([('name', '=', str(bill.invoice_origin))], limit=1)
            # bill.total_bank_payment = bill.purchase_id.total_bank_payment if bill.purchase_id else purchase_order.total_cash_payment
            bill.total_bank_payment = purchase_order.total_bank_payment

class AccountPayment(models.Model):
    _inherit = 'account.payment'

    x_purchase_order_id = fields.Many2one('purchase.order', string='Purchase Order')
    x_payment_type = fields.Selection([
            ('cash', 'Cash'),
            ('bank', 'Bank')
        ], string='Payment Type', required=True, default='bank')

    @api.onchange('journal_id')
    def _onchange_journal_payment_type(self):
        if self.journal_id:
            if self.journal_id.type == 'cash':
                self.x_payment_type = 'cash'
            elif self.journal_id.type == 'bank':
                self.x_payment_type = 'bank'

    @api.model_create_multi
    def create(self, vals_list):
        for vals in vals_list:
            # Set payment type based on journal if not already set
            if 'x_payment_type' not in vals and 'journal_id' in vals:
                journal = self.env['account.journal'].browse(vals['journal_id'])
                if journal.type == 'cash':
                    vals['x_payment_type'] = 'cash'
                else:
                    vals['x_payment_type'] = 'bank'
        return super(AccountPayment, self).create(vals_list)

    def write(self, vals):
        # Update payment type if journal changes
        if 'journal_id' in vals:
            journal = self.env['account.journal'].browse(vals['journal_id'])
            if journal.type == 'cash':
                vals['x_payment_type'] = 'cash'
            else:
                vals['x_payment_type'] = 'bank'
        return super(AccountPayment, self).write(vals)

    @api.onchange('x_purchase_order_id', 'x_payment_type')
    def _onchange_purchase_order_payment_type(self):
        if self.x_purchase_order_id and self.x_payment_type:
            if self.x_payment_type == 'cash':
                self.amount = self.x_purchase_order_id.total_pending_cash
            else:
                self.amount = self.x_purchase_order_id.total_pending_bank

    def action_post(self):
        res = super(AccountPayment, self).action_post()
        for payment in self:
            if payment.x_purchase_order_id:
                if payment.x_payment_type == 'cash':
                    payment.x_purchase_order_id.total_cash_paid += payment.amount
                else:
                    payment.x_purchase_order_id.total_bank_paid += payment.amount
        return res